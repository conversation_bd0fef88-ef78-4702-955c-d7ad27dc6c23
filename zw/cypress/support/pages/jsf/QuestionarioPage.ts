class QuestionarioPage {
    elements = {
        EMAGRECIMENTO: '#form\\:questionarioCliente\\:0\\:tabela01\\:0\\:idPerguntaClienteMultipla',
        MUSCULACAO: '#form\\:questionarioCliente\\:1\\:tabela01\\:0\\:idPerguntaClienteMultipla',
        MANHA: '#form\\:questionarioCliente\\:2\\:tabela01\\:0\\:idPerguntaClienteMultipla',
        AMIGOS: '#form\\:questionarioCliente\\:3\\:tabela01\\:0\\:idPerguntaClienteMultipla',
        SELECT_CONSULTOR: '#form\\:consultor',
        ATUALIZAR_BV: '#form\\:cadastrarVisitante'
    }

    atualizarBV(): void {
        cy.get(this.elements.SELECT_CONSULTOR, {timeout: 10000}).should('be.visible');
        cy.get(this.elements.SELECT_CONSULTOR).select('PACTO - MÉTODO DE GESTÃO')
        cy.get(this.elements.EMAGRECIMENTO).click()
        cy.get(this.elements.MUSCULACAO).click()
        cy.get(this.elements.MANHA).click()
        cy.get(this.elements.AMIGOS).click()
        cy.get(this.elements.ATUALIZAR_BV).click()

    }
}

export default new QuestionarioPage();
