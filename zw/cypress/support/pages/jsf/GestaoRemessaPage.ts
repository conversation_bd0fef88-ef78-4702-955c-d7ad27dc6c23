class GestaoRemessaPage {
    elements = {
        BOTAO_CONSULTAR: '#form\\:consultarRemessas',
        FECHAR_REMESSA: '#form\\:tblRemessas\\:0\\:fecharRemessa',
        BOTAO_SIM_FECHAR_REMESSA: '#formMdlMensagemGenerica\\:sim'
    }

    fecharRemessa(): void {
        cy.wait(3000)
        cy.get(this.elements.BOTAO_CONSULTAR, {timeout: 10000}).should('be.visible').click({force: true});
        cy.get(this.elements.FECHAR_REMESSA, {timeout: 10000}).should('be.visible').click({force: true});
        cy.get(this.elements.BOTAO_SIM_FECHAR_REMESSA, {timeout: 10000}).should('be.visible').click({force: true});
        cy.get(this.elements.FECHAR_REMESSA, {timeout: 10000}).should('not.exist');
        //Não deve aparecer o botão de fechar, após ser fechado a remessa
    }
}

export default new GestaoRemessaPage();
