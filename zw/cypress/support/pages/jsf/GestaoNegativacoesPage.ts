class GestaoNegativacoesPage {
    elements = {
        ICONE_CONSULTAR_CLIENTE: '#v20_form\\:v20_consultarCliente',
        BOTAO_CONSULTAR_CLIENTE: '#v20_formCliente\\:v20_btnConsultarCliente',
        DESCRICAO_CLIENTE: '#v20_formCliente\\:v20_valorConsultaCliente',
        MENSAGEM_RETORNO: '.snotifyToast__body ng-star-inserted',
        BOTAO_CONSULTAR: '#v20_form\\:consultar',
        INICIO_VENCIMENTO: ' #v20_form\\:v20_dataInicioVencimentoInputDate',
        TERMINO_VENCIMENTO: '#v20_form\\:v20_dataTerminoVencimentoInputDate',
        LISTAGEM: '#v20_form\\:listagemParcelas',
        LIMPAR_CLIENTE: '#v20_form\\:v20_LimparCliente',
        SITUACAO: '#v20_form\\:v20_situacao'

    }

    pesquisarGestaoNegativacoes(): void {
        cy.wait(3000)
        cy.get(this.elements.INICIO_VENCIMENTO, {timeout: 30000}).should('be.visible').clear()
        cy.get(this.elements.TERMINO_VENCIMENTO, {timeout: 30000}).should('be.visible').clear()
        cy.get(this.elements.ICONE_CONSULTAR_CLIENTE, {timeout: 30000}).should('be.visible').click()
        cy.get(this.elements.DESCRICAO_CLIENTE, {timeout: 30000}).should('be.visible').type('TESTE REMESSA CIELO')
        cy.get(this.elements.BOTAO_CONSULTAR_CLIENTE, {timeout: 30000}).should('be.visible').click()
        cy.wait(3000)
        cy.contains('TESTE REMESSA CIELO').should('be.visible').click()
        cy.wait(3000)
        cy.get(this.elements.BOTAO_CONSULTAR, {timeout: 30000}).should('be.visible').click()
        cy.wait(300)
        cy.get(this.elements.LISTAGEM, {timeout: 30000}).should('be.visible').should('contain', 'TESTE REMESSA CIELO')
        cy.get(this.elements.LIMPAR_CLIENTE, {timeout: 30000}).should('be.visible').click()
        cy.get(this.elements.SITUACAO, {timeout: 30000}).should('be.visible').select('Pago')
        cy.get(this.elements.BOTAO_CONSULTAR, {timeout: 30000}).should('be.visible').click()
        cy.get(this.elements.LISTAGEM, {timeout: 30000}).should('be.visible').should('contain', 'Pago')
        cy.get(this.elements.SITUACAO, {timeout: 30000}).should('be.visible').select('Em Aberto')
        cy.get(this.elements.BOTAO_CONSULTAR, {timeout: 30000}).should('be.visible').click()
        cy.get(this.elements.LISTAGEM, {timeout: 30000}).should('be.visible').should('contain', 'Em Aberto')
        cy.get(this.elements.SITUACAO, {timeout: 30000}).should('be.visible').select('Cancelado')
        cy.get(this.elements.BOTAO_CONSULTAR, {timeout: 30000}).should('be.visible').click()
        cy.get(this.elements.LISTAGEM, {timeout: 30000}).should('be.visible').should('contain', 'Cancelado')
        cy.get(this.elements.SITUACAO, {timeout: 30000}).should('be.visible').select('Renegociado')
        cy.get(this.elements.BOTAO_CONSULTAR, {timeout: 30000}).should('be.visible').click()
        cy.get(this.elements.LISTAGEM, {timeout: 30000}).should('be.visible').should('contain', 'Renegociado')
    }

}


export default new GestaoNegativacoesPage();
