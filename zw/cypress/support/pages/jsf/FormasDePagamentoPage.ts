import * as faker from 'faker-br'

class FormasDePagamentoPage {

    elements = {
        DIV_PAGAMENTO_ESCOLHIDO: '#divFormaPagamentoEscolhido',
        EDITAR_DATA: '.fa-icon-edit',
        LIMPAR_DATA: '.fa-icon-eraser',
        DINHEIRO: '#form\\:opcoesFormasPagamento\\:0\\:movPagamentoEscolhido',
        BOLETO: '#form\\:opcoesFormasPagamento\\:1\\:movPagamentoEscolhido',
        CARTAO_CREDITO: '#form\\:opcoesFormasPagamento\\:2\\:movPagamentoEscolhido',
        CARTAO_DEBITO: '#form\\:opcoesFormasPagamento\\:3\\:movPagamentoEscolhido',
        CHEQUE: '#form\\:opcoesFormasPagamento\\:4\\:movPagamentoEscolhido',
        PIX: '#form\\:opcoesFormasPagamento\\:5\\:movPagamentoEscolhido',
        DEBITO: '#form\\:opcoesFormasPagamento\\:6\\:movPagamentoEscolhido',
        BOTAO_CONFIRMAR: '#form\\:btnConfirmar',
        INPUT_SENHA: '#formSenhaAutorizacao\\:senha',
        CONFIRMAR_SENHA: '#formSenhaAutorizacao\\:btnAutorizar',
        MSG_CONFIRMACAO: '#form\\:msgVerdeConfirmacao1',
        CONVENIO_BOLETO: '#form\\:opcoesFormasPagamento\\:1\\:convenio',
        ADQUIRENTE: '#form\\:opcoesFormasPagamento\\:2\\:adquirente',
        OPERADORA_CARTAO: '#form\\:opcoesFormasPagamento\\:2\\:operadoraCartao',
        PARCELAS: '#form\\:opcoesFormasPagamento\\:2\\:nrParcelaCartao',
        AUTORIZACAO: '#form\\:opcoesFormasPagamento\\:2\\:nrParcelaCartao',
        ADIQUIRENTE_DEBITO: '#form\\:opcoesFormasPagamento\\:3\\:adquirente',
        OPERADORA_CARTAO_DEBITO: '#form\\:opcoesFormasPagamento\\:3\\:operadoraCartao1',
        AUTORIZACAO_DEBITO: ':nth-child(4) > .inputTextClean',
        LOADER: '.rich-mpnl-body > img',
        TITULO_PAGINA: '.margin-box > .container-header-titulo',
        CADASTRAR_NOVO: '#form\\:btnNovo',
        DESCRICAO_FORMA_PAGAMENTO: '#form\\:descricao',
        TIPO_PAGAMENTO: '#form\\:tipoFormaPagamento',
        CONVENIO: '#form\\:convenio',
        EMPRESA: '#form\\:empresaselecionadafpgto > tbody > .linhaImpar > .classDireita > .block > .form',
        CONTA:'#form\\:contadestino',
        ADICIONAR_EMPRESA: '#form\\:adicionarCfgEmpresa',
        PERFIL_ACESSO:'#form\\:perfilAcesso',
        ADICIONAR_PERFIL_ACESSO: '#form\\:adicionarFormaPagamentoPerfilAcesso',
        SALVAR:'#form\\:salvar',
        VOLTAR: '#form\\:consultar',
        MENSAGEM_RETORNO: '#titleInfo',
        OPCAO_PAGAMENTO: '.opcaoPagamento',
        BOTAO_CADASTRAR_NOVO: '#form\\:btnNovo',
        INPUT_DESCRICAO: '#form\\:descricao',
        SELECT_TIPO_FORMA_PAGAMENTO: '#form\\:tipoFormaPagamento',
        CHECK_SOMENTE_FINANCEIRO: '#form\\:somenteFinanceiro',
        SELECT_PINPAD: '#form\\:pinpad',
        INPUT_DESCRICAO_PDV: '#form\\:descricaoPinpadCappta',
        INPUT_SERIAL_PDV: '#form\\:serialNumberCappta',
        CHECK_RECEBER_SOMENTE_PDV: ':nth-child(5) > .classDireita > input',
        BOTAO_ADICIONAR_PDV: '#form\\:adicionarPinpad',
        BOTAO_GRAVAR: '#form\\:salvar'
    }

    aguardarCarregamento() {
        cy.get(this.elements.TITULO_PAGINA, {timeout: 20000}).should('be.visible')
        cy.get(this.elements.TITULO_PAGINA).contains('Formas de Pagamento')
    }

    receberParcela(formaPagamento: string, semSenha: boolean = false) {
        cy.intercept('GET', '/ZillyonWeb/faces/pagamento.jsp').as('getPagamento')
        cy.intercept('POST', '/ZillyonWeb/faces/pagamento.jsp').as('postPagamento')
    
        cy.wait('@getPagamento', { timeout: 10000 })
        cy.get(this.elements.DIV_PAGAMENTO_ESCOLHIDO).should('be.visible', { timeout: 10000 }).wait(1000)
    
        switch (formaPagamento.toUpperCase()) {
            case 'DINHEIRO':
                cy.get(this.elements.DINHEIRO).check()
                break
    
            case 'BOLETO':
                cy.get(this.elements.BOLETO).should('exist').check()
                cy.get(this.elements.CONVENIO_BOLETO).should('be.visible').select('BOLETO')
                break
    
            case 'CARTÃO DE CRÉDITO':
                
                cy.get(this.elements.CARTAO_CREDITO).should('exist').check()
                cy.get(this.elements.LOADER).should('not.be.visible')
                cy.get(this.elements.ADQUIRENTE).should('be.visible').select('STONE')
                cy.get(this.elements.LOADER).should('not.be.visible')
                cy.get(this.elements.OPERADORA_CARTAO).should('be.visible').select('VISA (CRÉDITO)')
                cy.get(this.elements.LOADER).should('not.be.visible')
                cy.get(this.elements.PARCELAS).should('be.visible').select('12 vezes')
                cy.get(this.elements.LOADER).should('not.be.visible')

                if (semSenha) {
                    cy.get(this.elements.AUTORIZACAO).should('be.visible').type(faker.random.number())
                }
                break
    
            case 'CARTÃO DE DÉBITO':
                cy.get(this.elements.CARTAO_DEBITO).should('exist').check({ force: true })
                cy.get(this.elements.LOADER).should('not.be.visible')
                cy.get(this.elements.ADIQUIRENTE_DEBITO).should('be.visible').select('STONE')
                cy.get(this.elements.LOADER).should('not.be.visible')
                cy.get(this.elements.OPERADORA_CARTAO_DEBITO).should('be.visible').select('MAESTRO (DEBITO)')
                cy.get(this.elements.LOADER).should('not.be.visible')

                if (semSenha) {
                    cy.get(this.elements.AUTORIZACAO_DEBITO).should('be.visible').type(faker.random.number())
                }
                break
        }
    
        cy.wait('@postPagamento', { timeout: 10000 })
        cy.get(this.elements.BOTAO_CONFIRMAR).should('be.visible').click({ force: true })
    
        if (!semSenha) {
            cy.get(this.elements.INPUT_SENHA).should('be.visible').clear()
            cy.get(this.elements.INPUT_SENHA).type('123', { force: true })
            cy.get(this.elements.CONFIRMAR_SENHA).should('be.visible').click({ force: true })
        }
    
        cy.get(this.elements.MSG_CONFIRMACAO).contains('Pagamento Efetuado Com Sucesso.')
    }

    cadastrarFormDePagamento() {
        cy.wait(5000)
        
        const descricaoAleatoria = `Teste Auto ${Date.now()}`;

        cy.get(this.elements.CADASTRAR_NOVO).click();
        cy.get(this.elements.DESCRICAO_FORMA_PAGAMENTO).type(descricaoAleatoria);
        cy.get(this.elements.TIPO_PAGAMENTO).select('Pix')

        cy.wait(300)
        cy.get(this.elements.CONVENIO).should('contain', 'PIX BANCO DO BRASIL').select('PIX BANCO DO BRASIL');

        cy.get(this.elements.EMPRESA).select('NOME FANTASIA DA ACADEMIA')
        cy.get(this.elements.CONTA).select('B:1 / AG:111-0/ CC:1111-0');

        cy.get(this.elements.ADICIONAR_EMPRESA).click()

        cy.wait(300)
        cy.get(this.elements.SALVAR, { timeout: 10000 }).should('be.visible').click()
        cy.contains('Dados Gravados com Sucesso').should('be.visible')

        cy.get(this.elements.VOLTAR).should('be.visible').click()

    }

    editarFormaDePagamento(){
        cy.wait(5000)

        cy.contains('BOLETO BANCÁRIO', { timeout: 10000 }).should('be.visible').click()

        cy.get(this.elements.EMPRESA).select('NOME FANTASIA DA ACADEMIA')
        cy.get(this.elements.ADICIONAR_EMPRESA).click()

        cy.wait(300)
        cy.get(this.elements.SALVAR, { timeout: 10000 }).should('be.visible').click()
        cy.contains('Dados Gravados com Sucesso').should('be.visible')

    }

    selecionarFormaPagamento(nomeFormaPagamento) {
        cy.wait(3000);
        cy.get(this.elements.OPCAO_PAGAMENTO)
            .contains(nomeFormaPagamento)
            .parents(this.elements.OPCAO_PAGAMENTO)
            .find('input[type="checkbox"]')
            .check({ force: true });

    }


    validarBloqueioPinpad(formaPagamento: string) {
        cy.wait(3000);
        this.selecionarFormaPagamento(formaPagamento)
        cy.get(this.elements.BOTAO_CONFIRMAR).should('be.visible').click({ force: true })
        cy.get(this.elements.MENSAGEM_RETORNO, { timeout: 10000 }).should('be.visible').contains('Não Foi Possível Realizar esta Operação')
    }

    incluirTipoSomentePinpad(): string {
        const nomeForma = `F. PAGAMENTO ${faker.random.number()}`
        cy.get(this.elements.BOTAO_CADASTRAR_NOVO).click()
        cy.get(this.elements.INPUT_DESCRICAO).type(nomeForma)
        cy.get(this.elements.SELECT_TIPO_FORMA_PAGAMENTO).select('Cartão de Crédito').wait(1500)
        cy.get(this.elements.CHECK_SOMENTE_FINANCEIRO).uncheck()

        cy.get(this.elements.SELECT_PINPAD).select('Linx')
        cy.get(this.elements.INPUT_DESCRICAO_PDV).type(nomeForma)
        cy.get(this.elements.INPUT_SERIAL_PDV).type('123456')
        cy.get(this.elements.CHECK_RECEBER_SOMENTE_PDV).check()
        cy.get(this.elements.BOTAO_GRAVAR).click()
        return nomeForma
    }

    validarRecebimentoSomentePinpad(formaPagamento: string): void {
        cy.wait(5000)
        cy.contains(formaPagamento ,{ timeout: 10000 }).click().wait(1500)
        cy.get(this.elements.BOTAO_CONFIRMAR).click()
        cy.contains(`A forma de pagamento ${formaPagamento} deve ser recebida somente via pinpad.`)
    }


}

export default new FormasDePagamentoPage();
