class NovaContaAReceberPage {
    
    elements = {
        INPUT_PESSOA: '#formLanc\\:pessoa',
        SUGGESTION_PESSOA: '#formLanc\\:suggestionPessoa',
        INPUT_DESCRICAO: '#formLanc\\:descricao',
        INPUT_VALOR: '#formLanc\\:valor',
        SELECT_FORMA_PAGAMENTO: '#formLanc\\:formaPagamento',
        SELECT_CONTA: '#formLanc\\:contaSelectitem',
        BTN_SALVAR: '#formLanc\\:btnGravarLanc',
        MENSAGEM_SUCESSO: '#messageInfo',

        // Adicionados:
        INPUT_DATA_LANCAMENTO: '#formLanc\\:dtLancamentoInputDate',
        INPUT_DATA_VENCIMENTO: '#formLanc\\:dataVencimentoInputDate',
        ICONE_CALENDARIO_VENCIMENTO: '#formLanc\\:dataVencimentoPopupButton',
        CALENDARIO_VENCIMENTO: '#formLanc\\:dataVencimento',
        INPUT_DATA_COMPETENCIA: '#formLanc\\:dataCompetenciaInputDate',
        ICONE_CALENDARIO_COMPETENCIA: '#formLanc\\\:dataCompetenciaPopupButton',
        CALENDARIO_COMPETENCIA: '#formLanc\\:dataCompetencia',
        BTN_AGENDAR: '#formLanc\\:agendarRecebimento',
        HEADER_AGENDAMENTO: '#agendamentoFinanceiroPanelHeader',
        RADIO_AGENDAMENTO: '#agendamentoFinanceiroForm\\:radioAgendamento\\:0',
        BTN_GRAVAR_AGENDAMENTO: '#agendamentoFinanceiroForm\\:gravarAgendamentoFin',
        PANEL_AGENDAMENTO: '#formLanc\\:panelAgendamentos_body',
        BOTAO_QUITAR: '#formLanc\\:Quitar',
        CONFIRMAR_QUITACAO:'#formLancarPagamento\\:confirmacao',
        CONFIRMAR_ESTORNO: '#formConfirmaEstorno\\:sim',
        BOTAO_GRAVAR: '#formLanc\\:btnGravarLanc',

        ///SENHA
        INPUT_SENHA: '#formSenhaAutorizacao\\:senha',
        BTN_AUTORIZAR: '#formSenhaAutorizacao\\:btnAutorizar',

    }

    validarAcessoTela() {
        cy.wait(3000)
        cy.contains('Lançar Conta a Receber')
    }

    preencherLancamento(pessoa, descricao, valor, formaPagamento, conta) {
        this.validarAcessoTela()
        cy.get(this.elements.INPUT_PESSOA).should('be.visible').clear().type(pessoa).wait(1000)
        cy.contains(pessoa.toUpperCase()).click({force: true}).wait(600)
        cy.get(this.elements.INPUT_DESCRICAO).should('be.visible').clear().type(descricao)
        cy.get(this.elements.INPUT_VALOR).clear().type(valor).wait(300)
        cy.get(this.elements.SELECT_FORMA_PAGAMENTO).should('be.visible').select(formaPagamento).wait(1000)
        cy.get(this.elements.ICONE_CALENDARIO_VENCIMENTO).click().wait(1000)
        cy.get(this.elements.CALENDARIO_VENCIMENTO).contains('1').click().wait(1000)
        cy.get(this.elements.ICONE_CALENDARIO_COMPETENCIA).click().wait(1000)
        cy.get(this.elements.CALENDARIO_COMPETENCIA).contains('1').click().wait(1000)
        cy.get(this.elements.SELECT_CONTA).should('be.visible').select(conta)
    }

    agendarLancamento() {
        cy.get(this.elements.BTN_AGENDAR, { timeout: 3000 }).should('be.visible').click()
        cy.get(this.elements.HEADER_AGENDAMENTO, { timeout: 3000 }).should('be.visible')
        cy.get(this.elements.RADIO_AGENDAMENTO, { timeout: 3000 }).should('be.visible').click()
        cy.get(this.elements.BTN_GRAVAR_AGENDAMENTO).should('be.visible').click()
        cy.get(this.elements.PANEL_AGENDAMENTO, { timeout: 3000 }).should('be.visible')
    }

    salvarLancamento() {
        cy.get(this.elements.BTN_SALVAR).should('be.visible').click()
    }

    senhaLancamento() {
        cy.get(this.elements.INPUT_SENHA, { timeout: 3000 }).should('be.visible').type('123')
        cy.get(this.elements.BTN_AUTORIZAR).should('be.visible').click()
    }

    validarMensagemSucesso() {
        cy.get(this.elements.MENSAGEM_SUCESSO).should('be.visible').and('contain', 'Dados Gravados com Sucesso')
    }

    quitar() {
        cy.get(this.elements.BOTAO_QUITAR, { timeout: 3000 }).should('be.visible').click()
        cy.wait(300)
        this.senhaLancamento()
        cy.wait(300)
        cy.get(this.elements.CONFIRMAR_QUITACAO, { timeout: 3000 }).should('be.visible').click()
        cy.get(this.elements.MENSAGEM_SUCESSO).should('be.visible').and('contain', 'Dados Gravados com Sucesso')
        cy.contains('Estornar Recebimento')
    }

    estornar() {
        cy.contains('Estornar Recebimento', { timeout: 3000 }).should('be.visible').click()
        cy.wait(300)
        cy.get(this.elements.CONFIRMAR_ESTORNO, { timeout: 3000 }).should('be.visible').click()
        cy.wait(300)
        cy.contains('Clique em gravar para confirmar o estorno.', { timeout: 3000 })
        cy.get(this.elements.BOTAO_GRAVAR, { timeout: 3000 }).should('be.visible').click()
        cy.wait(300)
        this.senhaLancamento()
        cy.get(this.elements.MENSAGEM_SUCESSO).should('be.visible').and('contain', 'Dados Gravados com Sucesso')
    }
}

export default new NovaContaAReceberPage();
