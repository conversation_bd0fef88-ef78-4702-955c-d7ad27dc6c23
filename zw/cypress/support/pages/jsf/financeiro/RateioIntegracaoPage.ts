class RateioIntegracaoPage {

    elements = {
        TITULO: '.margin-box > .container-header-titulo',
        TIPO_RATEIO: '#formRateioEdicao\\:tipoRateio\\:0',
        PLANO: '#formRateioEdicao\\:btAddPlano',
        RECEITA: '#treeview\\:1\\:\\:descricaoDetalhada > .tituloCampos',
        VALOR: '#formRateioEdicao\\:valor',
        ADICIONAR_RATEIO: '#formRateioEdicao\\:botaoAdicionarRateio',
        SALVAR_RATEIO: '#formRateioEdicao\\:botaoSalvarRateios',
        MENSAGEM: '.mensagemDetalhada',
        EXCLUIR_RATEIO_0: '#formRateioEdicao\\:listaRateios\\:0\\:removerRateioEdicao',
        ADICIONAR_SERVICO: 'img[title="Adicionar Rateio"]'
    }

    validarAcesso(): void {
        cy.wait(5000)
        cy.get(this.elements.TITULO, { timeout: 30000 })
          .should('be.visible')
          .should('contain', 'Rateio Integração')
    }

    cadastroRateioServico(): void {
        this.validarAcesso()

        cy.contains('td', 'SERVIÇO')
          .closest('tr')
          .find(this.elements.ADICIONAR_SERVICO)
          .should('have.length', 1)
          .click()

        cy.get(this.elements.TIPO_RATEIO, { timeout: 30000 }).should('be.visible').click()
        cy.wait(300)
        cy.get(this.elements.PLANO, { timeout: 30000 }).should('be.visible').click()
        cy.get(this.elements.RECEITA, { timeout: 30000 }).should('be.visible').click()
        cy.wait(300)
        cy.get(this.elements.VALOR, { timeout: 30000 }).should('be.visible').clear().type('100,00')
        cy.get(this.elements.ADICIONAR_RATEIO, { timeout: 30000 }).should('be.visible').click()
        cy.wait(300)
        cy.get(this.elements.SALVAR_RATEIO, { timeout: 30000 }).should('be.visible').click()
    }

    validarMensagemRateioServico(): void {
        cy.wait(300)
        cy.get(this.elements.MENSAGEM, { timeout: 30000 })
          .should('be.visible')
          .should('contain', 'O total da soma das percentagens do tipo Plano de Contas deve ser igual a 100%.')
    }

    excluirRateioServico(): void {
        cy.get(this.elements.EXCLUIR_RATEIO_0, { timeout: 30000 }).should('be.visible').click()
        cy.wait(3000)
        cy.get(this.elements.EXCLUIR_RATEIO_0, { timeout: 30000 }).should('be.visible').click()
        cy.get(this.elements.SALVAR_RATEIO, { timeout: 30000 }).should('be.visible').click()
    }
}

export default new RateioIntegracaoPage()
