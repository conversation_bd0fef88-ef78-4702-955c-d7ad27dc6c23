class DREPage {
    elements = {
        TITULO_PAGINA: '.margin-box > .container-header-titulo',
        VISUALIZAR_RELATORIO: '#formDRE\\:demonstrativoFinanComThread',
        VISUALIZAR_IMPRESSAO :  '#formDRE\\:j_id_jsp_830833340_78'
    }

    validarAberturaPagina() {
        cy.wait(3000)
        cy.get(this.elements.TITULO_PAGINA, {timeout: 10000}).contains('Demonstração do resultado do exercício')
        
    }

    validarImpressao() {
        cy.get(this.elements.VISUALIZAR_RELATORIO, { timeout: 10000 })
       .scrollIntoView()
       .should('be.visible')
       .click();
        cy.wait(3000)
        cy.window().then((win) => {
            cy.stub(win, 'open').callsFake((url) => {
                win.location.href = url;
            });
        });
        cy.get(this.elements.VISUALIZAR_IMPRESSAO, {timeout: 10000}).should('be.visible').click()
        cy.contains('D.R.E. Financeiro')
        cy.contains('NOME FANTASIA DA ACADEMIA')
        cy.contains('Usuario: PACTO - MÉTODO DE GESTÃO')
        cy.contains('Resultado do exercício')
    }


    validarLancamentoFinanceiro(nomeFornecedor) {
        cy.get('#formDRE\\:fonteDadosDF').should('be.visible').select('Financeiro')
        cy.get('#formDRE\\:demonstrativoFinanComThread').should('be.visible').click()
        cy.wait(3000)
        cy.get('a.linkTabela[onclick^="preencherValorChamarBotao"]').first().click();
        cy.wait(3000)
        cy.get('#formModalLancamentosDF\\:tabelas').should('be.visible').contains(nomeFornecedor)
        cy.get('#formModalLancamentosDF\\:hidelinkLancamentos').should('be.visible').click()



    }
   
}


export default new DREPage();
