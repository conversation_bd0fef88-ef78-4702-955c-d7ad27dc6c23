
class ResumoContaPage {
    elements = {
        TITULO_PAGINA: '#form\\:nometela > .container-header-titulo', 
        BOTAO_VOLTAR: '#form\\:voltarContas'
       
    }

    validarAberturaPagina() {
        cy.wait(300)
        cy.get(this.elements.TITULO_PAGINA, {timeout: 10000}).contains('Resumo de Contas')
        
    }

    acessarConta(nomeConta) {
     this.validarAberturaPagina()
        cy.contains(nomeConta, {timeout: 10000}).should('be.visible').click()
        cy.wait(3000)
        cy.get(this.elements.TITULO_PAGINA, {timeout: 10000}).contains('Movimentação da Conta - '+nomeConta)
    }

    
    voltar() {
        cy.wait(300)
        cy.get(this.elements.BOTAO_VOLTAR, {timeout: 10000}).should('be.visible').click()
        cy.wait(3000)
        this.validarAberturaPagina()
   
    }

}


export default new ResumoContaPage();