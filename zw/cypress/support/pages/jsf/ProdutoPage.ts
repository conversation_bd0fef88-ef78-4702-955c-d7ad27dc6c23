import * as faker from 'faker-br'

class ProdutoPage {

    elements = {
        BUSCAR_PRODUTO: '[class="fa-icon-search searchbox-icon"]',
        RESULTADO_BUSCA: '.odd > :nth-child(2)',
        PRE_PAGO: '#form\\:valorPacote',
        POS_PAGO: '#form\\:valorPacotePos',
        SALVAR: '#form\\:salvar',
        MENSAGEM: '#form\\:msgProduto',
        NOVO: '#form\\:btnNovo',
        DESCRICAO: '#form\\:descricao',
        TIPO_PRODUTO: '#form\\:tipoProduto',
        CATEGORIA: '#form\\:categoriaProduto',
        VALOR: '#form\\:valor',
        ADD_PACOTE: '#form\\:addPacote',
    }

    cadastrarProdutoCreditoPersonal(): void {
        const nomeProdutoEsperado = 'Crédito Personal Interno';

        cy.get(this.elements.NOVO, {timeout: 10000}).click()
        cy.get(this.elements.DESCRICAO).type(nomeProdutoEsperado)
        cy.get(this.elements.CATEGORIA).select('5', {force: true})
        cy.get(this.elements.TIPO_PRODUTO).select('Crédito Personal')
        cy.get(this.elements.VALOR).type('1000')
        cy.get(this.elements.PRE_PAGO).type('150,00')
        cy.get(this.elements.POS_PAGO).type('200,00')

        cy.get(this.elements.ADD_PACOTE).should('be.visible').click();

        cy.wait(3000);

        cy.get(this.elements.PRE_PAGO).type('70,00')
        cy.get(this.elements.POS_PAGO).type('80,00')
        cy.get(this.elements.ADD_PACOTE).should('be.visible').click();

        cy.wait(3000);

        cy.contains('Gravar').should('be.visible').click();

        cy.get(this.elements.MENSAGEM)
            .should('be.visible')
            .and('have.text', 'Dados Gravados com Sucesso');
    }

    cadastrarProdutoLocacao(valor: string): Cypress.Chainable {
        const nomeProduto = faker.name.findName()
        cy.get(this.elements.NOVO).click()
        cy.get(this.elements.DESCRICAO).type(nomeProduto)
        cy.get(this.elements.TIPO_PRODUTO).select('Locação').wait(500)
        cy.get(this.elements.CATEGORIA).select('SERVIÇOS')
        cy.get(this.elements.VALOR).type(valor)
        cy.get(this.elements.SALVAR).click()
        return cy.wrap(nomeProduto)
    }
}

export default new ProdutoPage();
