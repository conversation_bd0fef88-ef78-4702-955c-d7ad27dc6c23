class TipoRemessaPage {

    elements = {

        BOTAO_CADASTRAR_NOVO: '#form\\:btnNovo',
        DESCRICAO: '#form\\:descricao',
        TIPO_RETORNO: '#form\\:tipoRetorno',
        TIPO_REMESSA: '#form\\:tipoRemessa',
        ARQUIVO_REMESSA: '#form\\:arquivoLayoutRemessa',
        BOTAO_GRAVAR: '#form\\:salvar',
        MENSAGEM: '.mensagem',
        BOTAO_VOLTAR: '#form\\:consultar',
        BOTAO_EXCLUIR: '#form\\:excluir',
        BOTAO_EXCLUIR_SIM: '#formMdlMensagemGenerica\\:sim'


    }

    cadastrarTipoRemessa(descricaoRemessa): void {

        cy.get(this.elements.BOTAO_CADASTRAR_NOVO, {timeout: 10000}).should('be.visible').click({force: true})
        cy.wait(300)
        cy.get(this.elements.DESCRICAO, {timeout: 10000}).should('be.visible').clear().type(descricaoRemessa)
        cy.get(this.elements.TIPO_RETORNO, {timeout: 10000}).should('be.visible').select('RETORNO PADRÃO DCC')
        cy.get(this.elements.TIPO_REMESSA, {timeout: 10000}).should('be.visible').clear().type(descricaoRemessa)
        cy.get(this.elements.ARQUIVO_REMESSA, {timeout: 10000}).should('be.visible').select('DCC/DCO')
        cy.get(this.elements.BOTAO_GRAVAR, {timeout: 10000}).should('be.visible').click({force: true})
        cy.get(this.elements.MENSAGEM, {timeout: 10000}).should('be.visible').should('contain', 'Dados Gravados com Sucesso')
        cy.get(this.elements.BOTAO_VOLTAR, {timeout: 10000}).should('be.visible').click({force: true})


    }

    editarTipoRemessa(descricaoRemessa): void {
        cy.wait(300)
        cy.contains(descricaoRemessa, {timeout: 10000}).should('be.visible').click({force: true})
        cy.get(this.elements.DESCRICAO, {timeout: 10000}).should('be.visible').clear().type(descricaoRemessa + 'EDITADO')
        cy.get(this.elements.BOTAO_GRAVAR, {timeout: 10000}).should('be.visible').click({force: true})
        cy.get(this.elements.MENSAGEM, {timeout: 10000}).should('be.visible').should('contain', 'Dados Gravados com Sucesso')
        cy.get(this.elements.BOTAO_VOLTAR, {timeout: 10000}).should('be.visible').click({force: true})


    }

    excluirTipoRemessa(descricaoRemessa): void {
        cy.wait(300)
        cy.contains(descricaoRemessa + 'EDITADO', {timeout: 10000}).should('be.visible').click({force: true})
        cy.get(this.elements.BOTAO_EXCLUIR, {timeout: 10000}).should('be.visible').click({force: true})
        cy.get(this.elements.BOTAO_EXCLUIR_SIM, {timeout: 10000}).should('be.visible').click({force: true})
        cy.get(this.elements.MENSAGEM, {timeout: 10000}).should('be.visible').should('contain', 'Dados Excluídos com Sucesso')


    }

}


export default new TipoRemessaPage();
