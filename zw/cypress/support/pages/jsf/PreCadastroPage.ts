class PreCadastroPage {

    elements = {
        INPUT_CLIENTE: '#form\\:termosConsulta',
        BOTAO_CONSULTAR: '#form\\:btnConsultarCliente',
        TRANSFERIR_CLIENTE: '#formExisteCliente\\:btnTransferirClienteEmpresa',
        DADOS_CLIENTE: '.dados-pessoais',
        BOTAO_CADASTRAR_NOVO: '#form\\:btnNovo',
        BOTAO_CONFIRMAR: '#form\\:salvar',
        SELECT_CONSULTOR: '#form\\:consultor',
        BOTAO_REALIZAR_NEGOCIACAO: '#form\\:realizarNegociacaoNova',
        BOTAO_CONFIRMAR_BV_INCOMPLETO: '#formGravarBV\\:btnSimRealizarNegociacao'
    }

    transferirAluno(cliente: string, unidade: string) {
        cy.get(this.elements.INPUT_CLIENTE).type(cliente)
        cy.get(this.elements.BOTAO_CONSULTAR).click()
        cy.contains('a', cliente).click()
        cy.get(this.elements.TRANSFERIR_CLIENTE).click().wait(2000)
        cy.get(this.elements.DADOS_CLIENTE, {timeout: 10000}).should('be.visible')
            .contains(unidade)
    }

    iniciarCadastroCliente(nomeAluno: string): void {
        cy.get(this.elements.INPUT_CLIENTE).type(nomeAluno)
        cy.get(this.elements.BOTAO_CONSULTAR).click()
        cy.get(this.elements.BOTAO_CADASTRAR_NOVO).click()
        cy.get(this.elements.BOTAO_CONFIRMAR).click()
        cy.get(this.elements.SELECT_CONSULTOR).select('PACTO - MÉTODO DE GESTÃO').wait(1500)
        cy.get(this.elements.BOTAO_REALIZAR_NEGOCIACAO).click()
        cy.get(this.elements.BOTAO_CONFIRMAR_BV_INCOMPLETO).click()
    }

}


export default new PreCadastroPage();
