import DateUtils from "@utils/DateUtils"

class TaxaDeComissaoPage {

    elements = {
        MENU_CADASTROS: '#sidebar-item-cadastros > .pacto-item-menu > .pacto-item-menu-content',
        MENU_TAXA_COMISSAO: '#sidebar-item-cadastroTaxaComissao > .pacto-item-menu > .pacto-item-menu-content',
        CADASTRAR_NOVO: '#form\\:btnNovo',
        SELECT_EMPRESA: '#form\\:empresa',
        RADIO_VENDEDOR: '#form\\:tipoCadastrar\\:0',
        MATRICULA: '#form\\:situacaoCadastrar\\:0',
        REMATRICULA: '#form\\:situacaoCadastrar\\:1',
        RENOVACAO: '#form\\:situacaoCadastrar\\:2',
        VIGENCIA_INICIAL: '#form\\:vigenciainicioInputDate',
        VIGENCIA_FINAL: '#form\\:vigenciafinalInputDate',
        MENSAL: '#form\\:dtgDuracao\\:0\\:slctModalide',
        TRIMESTRAL: '#form\\:dtgDuracao\\:1\\:slctModalide',
        ANUAL: '#form\\:dtgDuracao\\:2\\:slctModalide',
        FIXO_ESPONTANEO: '#form\\:fixoEsp',
        FIXO_AGENDADO: '#form\\:fixoAg',
        PERCENTUAL_ESPONTANEO: '#form\\:percEsp',
        PERCENTUAL_AGENDADO: '#form\\:percAg',
        GRAVAR: '#form\\:salvar',
        EXCLUIR: '#form\\:excluir',
        VOLTAR_PARA_LISTA: '#form\\:consultar',
        LISTA_TAXAS: '#tblComissaoGeralConfiguracao_wrapper',
        EXCLUIR_TAXA: '.fa-icon-trash',
        CONFIRMAR_EXCLUSAO: '#formExclusaoTudo\\:sim'
    }


    cadastrarTaxa(): void {
        const dataInicial = DateUtils.getData()
        const dataFinal = DateUtils.getData(365)

        cy.get(this.elements.MENU_CADASTROS).click()
        cy.capturaPopUp()
        cy.get(this.elements.MENU_TAXA_COMISSAO).click()
        cy.get(this.elements.CADASTRAR_NOVO).click()


        cy.get(this.elements.SELECT_EMPRESA).select('NOME FANTASIA DA ACADEMIA').wait(500)
        // cy.get(this.elements.RADIO_VENDEDOR).should('be.visible');
        // cy.get(this.elements.RADIO_VENDEDOR).click();
        cy.get(this.elements.MATRICULA).click()
        cy.get(this.elements.VIGENCIA_INICIAL).type(dataInicial)
        cy.get(this.elements.VIGENCIA_FINAL).type(dataFinal)
        cy.get(this.elements.ANUAL).click()
        cy.get(this.elements.PERCENTUAL_AGENDADO).type('2500')
        cy.get(this.elements.FIXO_ESPONTANEO).type('2000')
        cy.get(this.elements.GRAVAR).click()

        cy.get(this.elements.LISTA_TAXAS).contains('25.0%')
    }

    editarTaxa(): void {
        cy.get(this.elements.LISTA_TAXAS).contains('25.0%').click()
        cy.get(this.elements.PERCENTUAL_AGENDADO).clear().type('5000')
        cy.get(this.elements.GRAVAR).click()
        cy.get(this.elements.LISTA_TAXAS).contains('50.0%')
    }

    excluirTaxa(): void {
        cy.get(this.elements.EXCLUIR_TAXA).first().click()
        cy.get(this.elements.CONFIRMAR_EXCLUSAO).should('exist').click({force: true})
        cy.contains('Dados Excluídos com Sucesso').should('be.visible')
    }
}


export default new TaxaDeComissaoPage();
