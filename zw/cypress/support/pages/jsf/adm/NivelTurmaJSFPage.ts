import * as faker from 'faker-br'
import { NivelTurmaJSFPage } from '../locators';

class NivelTurmaPage {
    nomeNivel: string;

    elements = {
        NOVO: '#form\\:btnNovo',
        DESCRICAO: '#form\\:descricao',
        SALVAR: '#form\\:salvar',
        MENSAGEM: '#form\\:msgNivel',
        VOLTAR: '#form\\:consultar',
        EXCLUIR: '#form\\:excluir',
        SIM: '#formMdlMensagemGenerica\\:sim'
    }

    cadastrarNivelTurma() {
        cy.wait(5000);
        this.nomeNivel = 'Novo nível ' + faker.random.number();

        cy.get(NivelTurmaJSFPage.NOVO).click();

        cy.get(NivelTurmaJSFPage.DESCRICAO)
            .should('be.visible')
            .clear()
            .type(this.nomeNivel);

        cy.get(NivelTurmaJSFPage.SALVAR).should('be.visible').click();

        cy.get(NivelTurmaJSFPage.MENSAGEM)
            .should('be.visible')
            .and('have.text', 'Dados Gravados com Sucesso');

        cy.get(NivelTurmaJSFPage.VOLTAR).click();
    }


    editarNivelTurma() {
        cy.wait(5000);

        cy.contains(new RegExp(this.nomeNivel, 'i'))
            .should('be.visible')
            .click();


        const novoNome = 'Nível editado ' + faker.random.number();
        this.nomeNivel = novoNome;


        cy.get(NivelTurmaJSFPage.DESCRICAO)
            .should('be.visible')
            .clear()
            .type(novoNome);

        cy.get(NivelTurmaJSFPage.SALVAR).should('be.visible').click();

        cy.get(NivelTurmaJSFPage.MENSAGEM)
            .should('be.visible')
            .and('contain.text', 'Dados Gravados com Sucesso');

        cy.get(NivelTurmaJSFPage.VOLTAR).click();
    }

    excluirNivelTurma() {

        cy.contains(new RegExp(this.nomeNivel, 'i'))
            .should('be.visible')
            .click()

        cy.get(NivelTurmaJSFPage.EXCLUIR).should('be.visible').click()
        cy.get(NivelTurmaJSFPage.SIM).should('be.visible').click()
        cy.contains('Dados Excluídos com Sucesso').should('be.visible')


    }
}

export default new NivelTurmaPage();