import * as faker from 'faker-br'

class NivelTurmaPage {
    nomeNivel: string;

    elements = {
        NOVO: '#form\\:btnNovo',
        DESCRICAO: '#form\\:descricao',
        SALVAR: '#form\\:salvar',
        MENSAGEM: '#form\\:msgNivel',
        VOLTAR: '#form\\:consultar',
        EXCLUIR: '#form\\:excluir',
        SIM: '#formMdlMensagemGenerica\\:sim'
    }

    cadastrarNivelTurma() {
        cy.wait(5000);
        this.nomeNivel = 'Novo nível ' + faker.random.number();

        cy.get(this.elements.NOVO).click();

        cy.get(this.elements.DESCRICAO)
            .should('be.visible')
            .clear()
            .type(this.nomeNivel);

        cy.get(this.elements.SALVAR).should('be.visible').click();

        cy.get(this.elements.MENSAGEM)
            .should('be.visible')
            .and('have.text', 'Dados Gravados com Sucesso');

        cy.get(this.elements.VOLTAR).click();
    }


    editarNivelTurma() {
        cy.wait(5000);

        cy.contains(new RegExp(this.nomeNivel, 'i'))
            .should('be.visible')
            .click();


        const novoNome = 'Nível editado ' + faker.random.number();
        this.nomeNivel = novoNome;


        cy.get(this.elements.DESCRICAO)
            .should('be.visible')
            .clear()
            .type(novoNome);

        cy.get(this.elements.SALVAR).should('be.visible').click();

        cy.get(this.elements.MENSAGEM)
            .should('be.visible')
            .and('contain.text', 'Dados Gravados com Sucesso');

        cy.get(this.elements.VOLTAR).click();
    }

    excluirNivelTurma() {

        cy.contains(new RegExp(this.nomeNivel, 'i'))
            .should('be.visible')
            .click()

        cy.get(this.elements.EXCLUIR).should('be.visible').click()
        cy.get(this.elements.SIM).should('be.visible').click()
        cy.contains('Dados Excluídos com Sucesso').should('be.visible')


    }
}

export default new NivelTurmaPage();