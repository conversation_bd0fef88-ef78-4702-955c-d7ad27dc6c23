import DateUtils from "@utils/DateUtils"
import { NegociacaoJSFPage } from '../../../locators';

class NegociacaoJSFPageClass {
  private static readonly TIMEOUT = 10000

  private static readonly ELEMENTS = {
    SELECT_PLANO: '#form\\:plano',
    LOADER: '.rich-mpnl-body > img',
    DURACAO_MENSAL: '#form\\:planoDuracaoVO\\:0\\:selectDuracaoPlano',
    EM_12_VEZES: '#form\\:planoCondicaoPagamentoVO\\:12\\:btnMarcarCondicaoPagamento > .fa-icon-circle-blank',
    CONFERIR_NEGOCIACAO: '#form\\:btnConferirNegociacao',
  }

  montarNegociacao(nomePlano: string): void {
    cy.get(NegociacaoJSFPage.SELECT_PLANO).select(nomePlano)
    cy.get(NegociacaoJSFPage.LOADER).should('not.be.visible')
    cy.get(NegociacaoJSFPage.DURACAO_MENSAL).should('not.be.visible').check({ force: true })
    cy.get(NegociacaoJSFPage.EM_12_VEZES).click({ force: true })
    cy.get(NegociacaoJSFPage.LOADER).should('not.be.visible').wait(1000)
    cy.get(NegociacaoJSFPage.CONFERIR_NEGOCIACAO).should('be.visible').click()
  }


}

export default new NegociacaoJSFPage()