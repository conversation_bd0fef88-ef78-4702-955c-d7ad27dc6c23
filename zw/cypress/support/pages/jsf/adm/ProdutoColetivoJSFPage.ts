import * as faker from 'faker-br'
import { ProdutoColetivoJSFPage } from '../../locators';

class ProdutoColetivoPage {

    elements = {
        NOVO: '#form\\:btnNovo',
        DESCRICAO: '#form\\:descricao',
        PRODUTO: '#form\\:produto',
        SUGESTAO: '.richfaces_suggestionEntry .rich-sb-cell-padding',
        PLANO: '#form\\:plano',
        SUGESTAO_PLANO: '#form\\:suggestionPlano\\:suggest > tbody > .richfaces_suggestionEntry > .rich-sb-cell-padding',
        LANCAR_PRODUTO: ':nth-child(11) > .classDireita > .tooltipster',
        SALVAR: '#form\\:salvar',
        SENHA: '#formSenhaAutorizacao\\:senha',
        CONTRATO_SEM_PRODUTO: '#form\\:j_id_jsp_1290575515_28\\:3',
        CONFIRMAR: '#formSenhaAutorizacao\\:btnAutorizar'
    }

    lancamentoProdutoColetivo() {
        cy.wait(5000);
        
        const nomeProduto = faker.commerce.productName();

        cy.get(ProdutoColetivoJSFPage.NOVO).click()
        cy.get(ProdutoColetivoJSFPage.DESCRICAO).type(nomeProduto)
        cy.get(ProdutoColetivoJSFPage.PRODUTO).type('WHEY GOLD')

        cy.get(ProdutoColetivoJSFPage.SUGESTAO)
            .contains('WHEY GOLD')
            .click();

        cy.wait(1000)
        cy.get(ProdutoColetivoJSFPage.CONTRATO_SEM_PRODUTO).click()

        cy.wait(1000)
        cy.get(ProdutoColetivoJSFPage.PLANO).type('FAZ TUDO')

        cy.get(ProdutoColetivoJSFPage.SUGESTAO_PLANO)
            .contains('FAZ TUDO')
            .click();

        cy.wait(1000)
        cy.get(ProdutoColetivoJSFPage.LANCAR_PRODUTO)
            .should('exist')
            .check()
            .wait(1500)

        cy.wait(100)
        cy.get(ProdutoColetivoJSFPage.SALVAR)
            .should('be.visible')
            .click()

        cy.get(ProdutoColetivoJSFPage.SENHA)
        .should('be.visible')
        .type('123')
        .wait(1500)

        cy.get(ProdutoColetivoJSFPage.CONFIRMAR)
            .should('be.visible')
            .click()

        cy.contains('Dados Gravados com Sucesso')
            .should('be.visible')

    }


}


export default new ProdutoColetivoPage();