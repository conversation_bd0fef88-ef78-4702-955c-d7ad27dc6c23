import DateUtils from "@utils/DateUtils"
import { FechamentoNegociacaoJSFPage } from '../../locators';

class FechamentoNegociacaoJSFPageClass {
  private static readonly TIMEOUT = 10000

  private static readonly ELEMENTS = {
    PRO_RATA: '#form\\:diasVencimento',
    VALOR_PRIMEIRA_PARCELA: '#form\\:movProduto\\:0\\:totalMensalidade',
    CONCLUIR: '#form\\:botaoConfirmarcao',
    INPUT_SENHA: '#formSenhaAutorizacao\\:senha',
    CONFIRMAR_SENHA: '#formSenhaAutorizacao\\:btnAutorizar',
    LOADER: '.rich-mpnl-body > img',
  }

  validarProRata(dias: number, valor: string) {
    cy.get(FechamentoNegociacaoJSFPage.PRO_RATA, { timeout: FechamentoNegociacaoJSFPage.TIMEOUT }).select(DateUtils.futureDayOfMonth(dias))
    cy.get(FechamentoNegociacaoJSFPage.VALOR_PRIMEIRA_PARCELA).should('contain', valor)
  }

  concluirNegociacao(): void {
    cy.get(FechamentoNegociacaoJSFPage.CONCLUIR).click()
    cy.get(FechamentoNegociacaoJSFPage.LOADER).should('not.be.visible')
    cy.get(FechamentoNegociacaoJSFPage.INPUT_SENHA).type('123')
    cy.get(FechamentoNegociacaoJSFPage.CONFIRMAR_SENHA).click()
  }

}

export default new FechamentoNegociacaoJSFPage()