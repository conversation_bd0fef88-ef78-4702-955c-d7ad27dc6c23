import * as faker from 'faker-br'

class UsuarioPage {
    elements = {
        CADASTRAR_NOVO: '#form\\:btnNovo',
        TIPO_USUARIO: '#form\\:tipoUsuario',
        NOME_COLABORADOR: '#form\\:nomecolaborador',
        DATA_NASCIMENTO: '#form\\:dataNascInputDate',
        CPF: '#form\\:cfp',
        EMAIL_COLABORADOR: '#form\\:emailColaborador',
        TIPO_COLABORADOR: '#form\\:tipoColaborador',
        ADD_TIPO_COLABORADOR: '#form\\:addTipoColaborador',
        USER_NAME: '#form\\:username',
        EMPRESA: '#form\\:empresa',
        COD_PERFIL_ACESSO: '#form\\:codPerfilAcesso',
        ADD_PERFIL_ACESSO: '#form\\:addPerfilAcesso',
        SALVAR: '#form\\:salvar',
        PIN: '.classDireita > table > tbody > tr > td > .form',
        SELECT_PERFIL_ACESSO_TREINO: '#form\\:codPerfilAcessoTreino',
        CONSULTAR_COLABORADOR: '#form\\:consultaDadosColaborador',
        INPUT_CONSULTAR_COLABORADOR: '#formColaborador\\:valorConsultaColaborador',
        CONSULTAR_COLABORADOR_MODAL: '#formColaborador\\:btnConsultarColaborador',
        USUARIO_ADRIANA: '.index-13 > :nth-child(2)',
    }

    cadastrarUsuarioComNovoColaborador(): string {
        const nomeColaborador = faker.name.findName()
        cy.get(this.elements.CADASTRAR_NOVO).click()
        cy.get(this.elements.TIPO_USUARIO).select('NC')
        cy.get(this.elements.NOME_COLABORADOR, {timeout: 10000}).type('Novo colaborador ' + faker.random.number())
        cy.get(this.elements.DATA_NASCIMENTO).type('10/10/2000').wait(800)
        cy.get(this.elements.CPF, {timeout: 10000}).type(faker.br.cpf())
        cy.get(this.elements.EMAIL_COLABORADOR).type(faker.internet.email())
        cy.get(this.elements.USER_NAME).type('colaborador' + faker.random.number())
        cy.get(this.elements.PIN).type('19283')
        cy.get(this.elements.TIPO_COLABORADOR).select('AD')
        cy.get(this.elements.ADD_TIPO_COLABORADOR).click().wait(800)
        cy.get(this.elements.EMPRESA).select('1', {timeout: 10000})
        cy.get(this.elements.COD_PERFIL_ACESSO, {timeout: 10000}).select('1')
        cy.get(this.elements.ADD_PERFIL_ACESSO, {timeout: 10000}).click().wait(500)
        cy.get(this.elements.SELECT_PERFIL_ACESSO_TREINO, {timeout: 10000}).select('COORDENADOR').wait(500)
        cy.get(this.elements.SALVAR, {timeout: 10000}).should('be.visible').click({force: true})
        cy.contains('Dados Gravados com Sucesso', {timeout: 10000})
        return nomeColaborador
    }

    cadastrarUsuarioComColaboradorExistente(colaborador: string): void {
        cy.get(this.elements.CADASTRAR_NOVO).click()
        cy.get(this.elements.TIPO_USUARIO).select('CE')
        cy.get(this.elements.CONSULTAR_COLABORADOR).click().wait(500)
        cy.get(this.elements.INPUT_CONSULTAR_COLABORADOR).should('be.visible').type(colaborador)
        cy.get(this.elements.CONSULTAR_COLABORADOR_MODAL).click().wait(500)
        cy.contains('a', colaborador.toUpperCase()).click().wait(500)
        cy.get(this.elements.CPF, {timeout: 10000}).type(faker.br.cpf())
        cy.get(this.elements.EMAIL_COLABORADOR).type(faker.internet.email())
        cy.get(this.elements.USER_NAME).type('colaborador' + faker.random.number())
        cy.get(this.elements.PIN).type('19283')
        cy.get(this.elements.TIPO_COLABORADOR).select('AD')
        cy.get(this.elements.ADD_TIPO_COLABORADOR).click().wait(500)
        cy.get(this.elements.EMPRESA).select('1')
        cy.get(this.elements.COD_PERFIL_ACESSO, {timeout: 10000}).select('1')
        cy.get(this.elements.ADD_PERFIL_ACESSO, {timeout: 10000}).click().wait(500)
        cy.get(this.elements.SELECT_PERFIL_ACESSO_TREINO, {timeout: 10000}).select('COORDENADOR', {force: true}).wait(500)
        cy.get(this.elements.SALVAR, {timeout: 10000}).should('be.visible').click({force: true})
        cy.contains('Dados Gravados com Sucesso', {timeout: 10000})
    }

    editarUsuario(): void {
        cy.get(this.elements.USUARIO_ADRIANA).click()
        cy.get(this.elements.SELECT_PERFIL_ACESSO_TREINO, {timeout: 10000}).select('COORDENADOR').wait(500)
        cy.get(this.elements.SALVAR, {timeout: 10000}).should('be.visible').click({force: true})
        cy.contains('Dados Gravados com Sucesso', {timeout: 10000})
    }

}


export default new UsuarioPage();
