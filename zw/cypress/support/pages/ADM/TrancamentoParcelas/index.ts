import { TrancamentoParcelasPage } from '../../../locators';
import dayjs from 'dayjs';
const nowTime = dayjs().add(2, 'days').format('DD/MM/YYYY');

class TrancamentoParcelasClass {

  acessarPaginaCliente(): void {
    
    cy.abrirFuncLupa('Segundo Trancamento Parcelas');

        // Verificar e desmarcar o checkbox se estiver marcado
        cy.get('#switch-nova-versao', { timeout: 15000 })
            .should('exist')
            .then($checkbox => {
                const isChecked = $checkbox.prop('checked');
                const hasCheckedClass = $checkbox.hasClass('checked');

                if (isChecked || hasCheckedClass) {
                    cy.get('.slider')
                        .should('be.visible')
                        .click({ force: true })
                        .then(() => {
                            cy.log('Checkbox desmarcado com sucesso.');
                        });
                }
            });
 
  }

  selecionarContrato(): void {
    cy.get(TrancamentoParcelasPage.selecionarContrato).should('be.visible').click();

    cy.window().then((win) => {
        
        cy.get(TrancamentoParcelasPage.linkAfastamento).should('be.visible').click();
        cy.stub(win, 'open', (url) => {
            win.location.href = Cypress.config().baseUrl + '/faces/' + url;
        }).as("popup");
    });

  }

  abrirLinkTrancamento(): void {
    
    cy.get(TrancamentoParcelasPage.linkTrancamento).should('be.visible').click({ force: true });
  }

  preencherDataTrancamento(data): void {
    
    cy.get(TrancamentoParcelasPage.dataTrancamentoInputDate).should('be.visible').type(nowTime);
  }

  clicarProximo(): void {
    
    cy.get(TrancamentoParcelasPage.proximo).should('be.visible').click({ force: true });
  }

  selecionarProduto(produtoId): void {
    
    cy.get(TrancamentoParcelasPage.produtoTrancamento).should('be.visible').select(produtoId);
  }

  selecionarJustificativa(justificativaId): void {
    
    cy.get(TrancamentoParcelasPage.justificativa).should('be.visible').select(justificativaId);
  }

  alterarParcelas(): void {
    
    cy.get(TrancamentoParcelasPage.alterarParcelas).should('be.visible').click();
  }

  confirmarAlteracoes(): void {
    
    cy.get(TrancamentoParcelasPage.confirmar).eq(0).should('be.visible').click({ force: true });
  }

  preencherSenhaAutorizacao(senha): void {
    
    cy.get(TrancamentoParcelasPage.senhaAutorizacao).should('be.visible').type(senha);
  }

  autorizarSenha(): void {
    
    cy.get(TrancamentoParcelasPage.btnAutorizar).should('be.visible').click();
  }

  verificarComprovante(mensagemEsperada): void {
    
    cy.get(TrancamentoParcelasPage.comprovanteOpTr).should($text => expect($text.text().trim()).to.equal(mensagemEsperada));
    
  } 

  voltarParaTelaAluno(): void {
    
    cy.loginComApi(1, 'teste', 'pactobr');
    cy.abrirFuncLupa('Segundo Trancamento Parcelas');
  }
  selecionarContratoParte2(): void {
    cy.get(TrancamentoParcelasPage.selecionarContrato).should('be.visible').click();

  }
  verificarDataHistoricoParcelas(): void {

    cy.get(TrancamentoParcelasPage.historicoParcelasDataVencimento).contains('06/10/2027');
  }
  
  clicarEstornarOperacao(): void {
    cy.get(TrancamentoParcelasPage.estornarOperacao).should('be.visible').click();
  }
  
  verMaisFinanceiroContrato(): void {
    cy.get(TrancamentoParcelasPage.linkVerMaisFinanceiro).should('be.visible').click();
  }
}

export default new TrancamentoParcelasClass();
