/**
 * Locators centralizados para as páginas do sistema
 * Organizados por funcionalidade para facilitar manutenção
 */

export const CadastroClientePage = {
    // Ações principais
    ADICIONAR_CLIENTE: '#taskbar-resource-add-person',
    CONSULTAR_CEP: '#add-cliente-cadastro-cep-buscar',
    BOTAO_CONSULTAR_CLIENTE: '#add-cliente-btn-consultar',
    PROSSEGUIR: '#btn-inf-cadastro-prosseguir',
    SALVAR_VISITANTE: '#add-cliente-salvar-visitante',
    REALIZAR_NEGOCIACAO: '#add-cliente-realizar-negociacao',
    TRANSFERIR_CLIENTE: '[data-cy="btn-mdl-transferir-cliente-empresa-transferir"] > .content',

    // Campos de dados pessoais
    CPF: '#add-cliente-cpf',
    NOME: '#add-cliente-cadastro-nome',
    RG: '#add-cliente-cadastro-rg',
    DATA_NASCIMENTO: '#ds3-btn-26 > .pct',
    TELEFONE: '#add-cliente-cadastro-telefone-input',
    EMAIL: '#add-cliente-cadastro-email',
    SEXO: '#add-cliente-cadastro-sexo > .ds3-select',
    PROFISSAO: '.icon-prof > .pct',

    // Campos de endereço
    CEP: '#add-cliente-cadastro-cep',
    ENDERECO: '#add-cliente-cadastro-endereco',

    // Campos de negociação e consultor
    CONSULTOR: '#add-cliente-boletim-consultor > .ds3-select',

    // Campos de autenticação
    SENHA: '#aut-input-psw',
    CONFIRMAR_SENHA: '#aut-btn-confirm',
    ENVIAR_LINK: '#btn-enviar > .content',

    // Campos de busca
    CAMPO_NOME_BUSCA: '#add-cliente-nome',
    CLIENTE: '.nome-cliente'
};

export const TelaDoClientePage = {
    BUSCA_GERAL: '#topbar-search-field',
    RESULTADO_BUSCA_GERAL: '#cat-autocomplete-0',
    PLANO_ALUNO: ':nth-child(4) > .column-cell > .ng-star-inserted',
    VALOR_PLANO: ':nth-child(6) > .detail-data-value > span',
    ABA_FINANCEIRO: '#pcc-tab-financeiro',
    PRIMEIRO_REGISTRO_COMPRAS: '#element-0-pch-fin-table-compras',
    PAGINADOR_PARCELAS: '#page-size-control-tbl-prod-cobrados-parcela',
    MODALIDADES: '.title-modalidade-turma',
    LISTA_TURMAS: '.modalidade-data',
    ABA_TREINO: '#pcc-tab-treino',
    CRIAR_PROGRAMA: '#renovar > .content',
    CRIAR_UM_NOVO: '#button-criar-novo-programa > .content',
    FECHAR_MODAL: '#close-aba',
    PROGRAMA_ATUAL: '#tr-btn-modal-programa',
    EXCLUIR_PROGRAMA: '#element-0-remove-tr-tbl-lista-programa',
    REMOVER: '#action-remover'
};

export const PaisPage = {
    ADICIONAR: '#btn-add-item',
    NOME: 'input[placeholder="Brasil"]',
    NACIONALIDADE: 'input[placeholder="Brasileira(o)"]',
    SIGLA: 'input[placeholder="GO"]',
    ESTADO: 'input[placeholder="Goiás"]',
    ADICIONAR_ESTADO: '.add-estado > span',
    BUSCA: '#input-busca-rapida',
    LISTA: '#element-0 > :nth-child(2)',
    EXCLUIR: '#element-0-action-delete\\ \\(key\\)'
};

export const CidadePage = {
    ADICIONAR: '#btn-add-item',
    NOME: '[title="Nome"]',
    PAIS: '.current-value',
    ESTADO: '.current-wrapper .option-label',
    FILTRO: '[placeholder="Filtro..."]',
    BUSCA: '#input-busca-rapida',
    EXCLUIR: '#element-0-action-delete\\ \\(key\\)'
};

export const BancoPage = {
    ADICIONAR: '#btn-add-item',
    NOME: '[title="Banco"]',
    CODIGO: '.ng-untouched.ng-pristine.ng-invalid',
    BUSCA: '#input-busca-rapida',
    EDITAR: '#element-0-editbanco',
    EXCLUIR: '#element-0-deletebanco'
};

export const ModalidadePage = {
    DESCRICAO: 'input[formcontrolname="nome"]',
    VALOR_MENSAL: 'input[formcontrolname="valorMensal"]',
    VEZES_NA_SEMANA: 'input[id="-input"]',
    BUSCA_RAPIDA: '#input-busca-rapida',
    VOLTAR: '#detalhamento-contrato-voltar',
    PRIMEIRA_LIXEIRA: '#element-0-excluir'
};

export const PerguntaPage = {
    ADICIONAR_PERGUNTA: '#btn-add-item',
    DESCRICAO: 'input[placeholder="Descrição"]',
    RESPOSTA: 'input[placeholder="resposta"]',
    ADICIONAR: '.add-resposta',
    EDITAR: 'i.pct-edit',
    BUSCA: '#input-busca-rapida',
    EXCLUIR: '#element-0-action-delete\\ \\(key\\)',
    TABELA_PERGUNTAS: '.table-content'
};

export const CampanhaPage = {
    CADASTRAR_CAMPANHA: '#form\\:btnCadastrarCampanha',
    NOME: '#formEditar\\:nomeBrinde',
    DATA_INICIO: '#formEditar\\:dataInicialInputDate',
    DATA_FIM: '#formEditar\\:dataFinalInputDate',
    DESCRICAO: '#formEditar\\:descricao',
    CATEGORIA: '#formEditar\\:tblComPontos\\:0\\:multipicadorCategoria',
    SALVAR: '#formEditar\\:salvar',
    EDITAR: '#form\\:listaCampanha\\:0\\:btnEditar',
    EXCLUIR: '#form\\:listaCampanha\\:0\\:btnExcluir1',
    SIM: '#formAviso\\:confirmacaoExclusaoCampanha'
};

export const ConvenioDescontoPage = {
    ADICIONAR: '#btn-add-item',
    BUSCA: '#input-busca-rapida',
    DESCRICAO: 'input[title="Descrição"]',
    VIGENCIA_FINAL: '[data-cy="cat-datepicker-2-input"]',
    ADICIONAR_LINHA: '#table-renderer-0-add-row',
    DESCRICAO_DURACAO: '#input-number-duracao',
    TIPO_DESCONTO: '#select-tipoDesconto',
    VALOR: '#input-decimal-valorDesconto'
};

export const PessoasPage = {
    PESSOAS: '#taskbar-resource-pessoas > .pct',
    EMPRESA: '#element-0 > :nth-child(4) > .column-cell > .empresaCell > :nth-child(1)'
};

export const DescontoPage = {
    ADICIONAR: '#btn-add-plano-desconto',
    DESCRICAO: '[data-cy="plano-novo-desconto-descricao-input"]',
    TIPO_PRODUTO: '[data-cy="plano-novo-desconto-tipo-produto"]',
    ATIVO: '#plano-novo-desconto-ativo > .cat-input-wrapper > label > .checkmark-outline',
    TIPO_DESCONTO: '.aux-parent > #plano-novo-desconto-tipo-desconto',
    VALOR: '.aux-wrapper > #plano-novo-desconto-tipo-desconto-valor'
};

export const AmbientePage = {
    CADASTRAR_NOVO: '#form\\:btnNovo',
    DESCRICAO_AMBIENTE: '#form\\:descricao',
    CAPACIDADE: '#form\\:capacidade',
    TIPO_AMBIENTE: '#form\\:tiposAmbiente',
    COLETOR: '#form\\:coletor',
    SITUACAO: '#form\\:situacao',
    SALVAR: '#form\\:salvar',
    VOLTAR: '#form\\:consultar'
};

export const ProdutoPage = {
    BUSCAR_PRODUTO: '[class="fa-icon-search searchbox-icon"]',
    RESULTADO_BUSCA: '.odd > :nth-child(2)',
    PRE_PAGO: '#form\\:valorPacote',
    POS_PAGO: '#form\\:valorPacotePos',
    SALVAR: '#form\\:salvar',
    MENSAGEM: '#form\\:msgProduto',
    NOVO: '#form\\:btnNovo',
    DESCRICAO: '#form\\:descricao',
    TIPO_PRODUTO: '#form\\:tipoProduto',
    CATEGORIA: '#form\\:categoriaProduto',
    VALOR: '#form\\:valor',
    EMPRESA: '#form\\:empresaCfg',
    VALOR_EMPRESA: '#form\\:valorempresaCfg',
    ADD_EMPRESA: '#form\\:addempresaCfg'
};

export const SorteioPage = {
    BOTAO_TODOS: '.check-selecionar-todos > pacto-cat-checkbox > .cat-input-wrapper > label > .checkmark-outline',
    BOTAO_CONFIG_REALIZARSORTEIO: '[class="ng-star-inserted"]',
    BOTAO_SALVAR_VOLTAR: '[class="row justify-content-end custom-buttons"]',
    BOTAO_VOLTAR_REVELAR: '[class="row justify-content-center custom-btns"]',
    BOTAO_VOLTAR_VALIDAR: '[class="row custom-btns"]'
};

export const MenuSuperiorPage = {
    // Esta page não tem elements fixos, usa seletores dinâmicos
    EXPLORAR_TOGGLE: '#topbar-modules-toggle'
};

export const PlanoPage = {
    ADICIONAR: '#btn-add-plano',
    CADASTRAR_PLANO_NORMAL: '#plano-normal',
    CADASTRAR_PLANO_RECORRENTE: '#plano-recorrencia > .content',
    CADASTRAR_PLANO_CREDITO: '#plano-credito > .content',
    CADASTRAR_PLANO_PERSONAL: '#plano-personal > .content',
    NOME: '#input-plano-nome',
    NOME_2: '[data-cy="input-plano-nome-input"]',
    ADICIONAR_LINHA: '[id^="table-renderer-0-add-row"]',
    ADICIONAR_LINHA_HORARIO: '#table-renderer-2-add-row',
    SELECT_PACOTE: '#select-table-duracao-plano-pacote',
    SELECT_MODALIDADE_NORMAL: '#select-table-duracao-plano-modalidade',
    SELECT_MODALIDADE: '#select-modalidade',
    SELECT_HORARIO: '#select-table-duracao-plano-horario',
    REPETICOES_NA_SEMANA: '#input-number-table-duracao-plano-vezesSemana'
};

export const NegociacaoPage = {
    // Cadastro aluno
    INCLUIR_CLIENTE: '#taskbar-resource-add-person > .pct',
    INPUT_CONSULTA: '#form\\:termosConsulta',
    BOTAO_CONSULTAR: '#form\\:btnConsultarCliente',
    BOTAO_CADASTRAR_NOVO: '#form\\:btnNovo',
    INPUT_DATA_NASCIMENTO: '#form\\:dataNascInputDate',
    MASCULINO: '#form\\:sexo\\:0',
    CONFIRMAR_VISITANTE: '#form\\:salvar',
    CONSULTOR_VISITATE: '#form\\:consultor',
    CADASTRAR_VISITANTE: '#form\\:cadastrarVisitante',
    CONFIRMAR_BV: '#formGravarBV\\:btnSimCadastrarVisitante',

    // Negociação
    BUSCA_RAPIDA: '[title="Busca rápida"]',
    INPUT_PLANO: '#input-plano',
    LISTA_CLIENTE: '.lista-cliente',
    SELECT_PLANO: '#input-plano',
    BTN_RECEBER: '#btn-receber',
    BTN_ENVIAR_LINK_PAGAMENTO: '#btn-enviar',
    INPUT_PASSWORD: '#aut-input-psw',
    BTN_CONFIRM: '.ng-star-inserted',
    SELECT_CONDICAO_PAGAMENTO: '#input-condicao',
    MODAL_SELECT_CONDICAO_PAGAMENTO: '[label="Condição de pagamento"]',
    INPUT_SENHA: '#aut-input-psw',
    BTN_CONFIRMAR_SENHA: '#aut-btn-confirm',
    CAMPO_DATA_INICIAL_CONTRATO: '.zebra-row > :nth-child(3)',
    CAMPO_DATA_FINAL_CONTRATO: '.zebra-row > :nth-child(4)',
    FECHAR_ABA: '.close-wrapper',
    FECHAR_MODAL: '#close-aba',

    // Elementos específicos
    ENVIAR_LINK_PAGAMENTO: '#btn-enviar > .content',
    SENHA: '#aut-input-psw',
    CONFIRMAR_SENHA: '#aut-btn-confirm',
    TABELA_PARCELAS_NEGOCIACAO: '.parcela-valor'
};

// ==================== LOCATORS ADM FALTANTES ====================

export const HomeAdmPage = {
    BOTAO_ADICIONAR_PESSOA: '#taskbar-resource-add-person > .pct',
    TROCAR_UNIDADE: '#menu-trocar-unidade',
    MODAL_TROCAR_UNIDADE: '.change-empesa-wrapper',
    NOME_EMPRESA: '.current-company',
    BOTAO_CONFIGURACAO: '#taskbar-bottom-nav-config',
    BOTAO_ALUNOS_FAVORITOS: '#topbar-alunos-favoritos > img',
    PRIMEIRO_ALUNO_RECENTE: ':nth-child(4) > :nth-child(1) > .col-12 > .row > .col-9 > .aluno-info > .line1 > a > .nomeAluno'
};