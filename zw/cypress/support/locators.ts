/**
 * Locators centralizados para as páginas do sistema
 * Organizados por funcionalidade para facilitar manutenção
 */

export const CadastroClientePage = {
    // Ações principais
    ADICIONAR_CLIENTE: '#taskbar-resource-add-person',
    CONSULTAR_CEP: '#add-cliente-cadastro-cep-buscar',
    BOTAO_CONSULTAR_CLIENTE: '#add-cliente-btn-consultar',
    PROSSEGUIR: '#btn-inf-cadastro-prosseguir',
    SALVAR_VISITANTE: '#add-cliente-salvar-visitante',
    REALIZAR_NEGOCIACAO: '#add-cliente-realizar-negociacao',
    TRANSFERIR_CLIENTE: '[data-cy="btn-mdl-transferir-cliente-empresa-transferir"] > .content',

    // Campos de dados pessoais
    CPF: '#add-cliente-cpf',
    NOME: '#add-cliente-cadastro-nome',
    RG: '#add-cliente-cadastro-rg',
    DATA_NASCIMENTO: '#ds3-btn-26 > .pct',
    TELEFONE: '#add-cliente-cadastro-telefone-input',
    EMAIL: '#add-cliente-cadastro-email',
    SEXO: '#add-cliente-cadastro-sexo > .ds3-select',
    PROFISSAO: '.icon-prof > .pct',

    // Campos de endereço
    CEP: '#add-cliente-cadastro-cep',
    ENDERECO: '#add-cliente-cadastro-endereco',

    // Campos de negociação e consultor
    CONSULTOR: '#add-cliente-boletim-consultor > .ds3-select',

    // Campos de autenticação
    SENHA: '#aut-input-psw',
    CONFIRMAR_SENHA: '#aut-btn-confirm',
    ENVIAR_LINK: '#btn-enviar > .content',

    // Campos de busca
    CAMPO_NOME_BUSCA: '#add-cliente-nome',
    CLIENTE: '.nome-cliente'
};

export const TelaDoClientePage = {
    BUSCA_GERAL: '#topbar-search-field',
    RESULTADO_BUSCA_GERAL: '#cat-autocomplete-0',
    PLANO_ALUNO: ':nth-child(4) > .column-cell > .ng-star-inserted',
    VALOR_PLANO: ':nth-child(6) > .detail-data-value > span',
    ABA_FINANCEIRO: '#pcc-tab-financeiro',
    PRIMEIRO_REGISTRO_COMPRAS: '#element-0-pch-fin-table-compras',
    PAGINADOR_PARCELAS: '#page-size-control-tbl-prod-cobrados-parcela',
    MODALIDADES: '.title-modalidade-turma',
    LISTA_TURMAS: '.modalidade-data',
    ABA_TREINO: '#pcc-tab-treino',
    CRIAR_PROGRAMA: '#renovar > .content',
    CRIAR_UM_NOVO: '#button-criar-novo-programa > .content',
    FECHAR_MODAL: '#close-aba',
    PROGRAMA_ATUAL: '#tr-btn-modal-programa',
    EXCLUIR_PROGRAMA: '#element-0-remove-tr-tbl-lista-programa',
    REMOVER: '#action-remover'
};

export const PaisPage = {
    ADICIONAR: '#btn-add-item',
    NOME: 'input[placeholder="Brasil"]',
    NACIONALIDADE: 'input[placeholder="Brasileira(o)"]',
    SIGLA: 'input[placeholder="GO"]',
    ESTADO: 'input[placeholder="Goiás"]',
    ADICIONAR_ESTADO: '.add-estado > span',
    BUSCA: '#input-busca-rapida',
    LISTA: '#element-0 > :nth-child(2)',
    EXCLUIR: '#element-0-action-delete\\ \\(key\\)'
};

export const CidadePage = {
    ADICIONAR: '#btn-add-item',
    NOME: '[title="Nome"]',
    PAIS: '.current-value',
    ESTADO: '.current-wrapper .option-label',
    FILTRO: '[placeholder="Filtro..."]',
    BUSCA: '#input-busca-rapida',
    EXCLUIR: '#element-0-action-delete\\ \\(key\\)'
};

export const BancoPage = {
    ADICIONAR: '#btn-add-item',
    NOME: '[title="Banco"]',
    CODIGO: '.ng-untouched.ng-pristine.ng-invalid',
    BUSCA: '#input-busca-rapida',
    EDITAR: '#element-0-editbanco',
    EXCLUIR: '#element-0-deletebanco'
};

export const ModalidadePage = {
    DESCRICAO: 'input[formcontrolname="nome"]',
    VALOR_MENSAL: 'input[formcontrolname="valorMensal"]',
    VEZES_NA_SEMANA: 'input[id="-input"]',
    BUSCA_RAPIDA: '#input-busca-rapida',
    VOLTAR: '#detalhamento-contrato-voltar',
    PRIMEIRA_LIXEIRA: '#element-0-excluir'
};

export const PerguntaPage = {
    ADICIONAR_PERGUNTA: '#btn-add-item',
    DESCRICAO: 'input[placeholder="Descrição"]',
    RESPOSTA: 'input[placeholder="resposta"]',
    ADICIONAR: '.add-resposta',
    EDITAR: 'i.pct-edit',
    BUSCA: '#input-busca-rapida',
    EXCLUIR: '#element-0-action-delete\\ \\(key\\)',
    TABELA_PERGUNTAS: '.table-content'
};

export const CampanhaPage = {
    CADASTRAR_CAMPANHA: '#form\\:btnCadastrarCampanha',
    NOME: '#formEditar\\:nomeBrinde',
    DATA_INICIO: '#formEditar\\:dataInicialInputDate',
    DATA_FIM: '#formEditar\\:dataFinalInputDate',
    DESCRICAO: '#formEditar\\:descricao',
    CATEGORIA: '#formEditar\\:tblComPontos\\:0\\:multipicadorCategoria',
    SALVAR: '#formEditar\\:salvar',
    EDITAR: '#form\\:listaCampanha\\:0\\:btnEditar',
    EXCLUIR: '#form\\:listaCampanha\\:0\\:btnExcluir1',
    SIM: '#formAviso\\:confirmacaoExclusaoCampanha'
};

export const ConvenioDescontoPage = {
    ADICIONAR: '#btn-add-item',
    BUSCA: '#input-busca-rapida',
    DESCRICAO: 'input[title="Descrição"]',
    VIGENCIA_FINAL: '[data-cy="cat-datepicker-2-input"]',
    ADICIONAR_LINHA: '#table-renderer-0-add-row',
    DESCRICAO_DURACAO: '#input-number-duracao',
    TIPO_DESCONTO: '#select-tipoDesconto',
    VALOR: '#input-decimal-valorDesconto'
};

export const PessoasPage = {
    PESSOAS: '#taskbar-resource-pessoas > .pct',
    EMPRESA: '#element-0 > :nth-child(4) > .column-cell > .empresaCell > :nth-child(1)'
};

export const HomeAdmPage = {
    BOTAO_ADICIONAR_PESSOA: '#taskbar-resource-add-person > .pct',
    TROCAR_UNIDADE: '#menu-trocar-unidade',
    MODAL_TROCAR_UNIDADE: '.change-empesa-wrapper',
    NOME_EMPRESA: '.current-company',
    BOTAO_CONFIGURACAO: '#taskbar-bottom-nav-config',
    BOTAO_ALUNOS_FAVORITOS: '#topbar-alunos-favoritos > img',
    PRIMEIRO_ALUNO_RECENTE: ':nth-child(4) > :nth-child(1) > .col-12 > .row > .col-9 > .aluno-info > .line1 > a > .nomeAluno'
};

export const DescontoPage = {
    ADICIONAR: '#btn-add-plano-desconto',
    DESCRICAO: '[data-cy="plano-novo-desconto-descricao-input"]',
    TIPO_PRODUTO: '[data-cy="plano-novo-desconto-tipo-produto"]',
    ATIVO: '#plano-novo-desconto-ativo > .cat-input-wrapper > label > .checkmark-outline',
    TIPO_DESCONTO: '.aux-parent > #plano-novo-desconto-tipo-desconto',
    VALOR: '.aux-wrapper > #plano-novo-desconto-tipo-desconto-valor'
};

export const AmbientePage = {
    CADASTRAR_NOVO: '#form\\:btnNovo',
    DESCRICAO_AMBIENTE: '#form\\:descricao',
    CAPACIDADE: '#form\\:capacidade',
    TIPO_AMBIENTE: '#form\\:tiposAmbiente',
    COLETOR: '#form\\:coletor',
    SITUACAO: '#form\\:situacao',
    SALVAR: '#form\\:salvar',
    VOLTAR: '#form\\:consultar'
};

export const ProdutoPage = {
    BUSCAR_PRODUTO: '[class="fa-icon-search searchbox-icon"]',
    RESULTADO_BUSCA: '.odd > :nth-child(2)',
    PRE_PAGO: '#form\\:valorPacote',
    POS_PAGO: '#form\\:valorPacotePos',
    SALVAR: '#form\\:salvar',
    MENSAGEM: '#form\\:msgProduto',
    NOVO: '#form\\:btnNovo',
    DESCRICAO: '#form\\:descricao',
    TIPO_PRODUTO: '#form\\:tipoProduto',
    CATEGORIA: '#form\\:categoriaProduto',
    VALOR: '#form\\:valor',
    EMPRESA: '#form\\:empresaCfg',
    VALOR_EMPRESA: '#form\\:valorempresaCfg',
    ADD_EMPRESA: '#form\\:addempresaCfg'
};

export const SorteioPage = {
    BOTAO_TODOS: '.check-selecionar-todos > pacto-cat-checkbox > .cat-input-wrapper > label > .checkmark-outline',
    BOTAO_CONFIG_REALIZARSORTEIO: '[class="ng-star-inserted"]',
    BOTAO_SALVAR_VOLTAR: '[class="row justify-content-end custom-buttons"]',
    BOTAO_VOLTAR_REVELAR: '[class="row justify-content-center custom-btns"]',
    BOTAO_VOLTAR_VALIDAR: '[class="row custom-btns"]'
};

export const MenuSuperiorPage = {
    // Esta page não tem elements fixos, usa seletores dinâmicos
    EXPLORAR_TOGGLE: '#topbar-modules-toggle'
};

export const PlanoPage = {
    ADICIONAR: '#btn-add-plano',
    CADASTRAR_PLANO_NORMAL: '#plano-normal',
    CADASTRAR_PLANO_RECORRENTE: '#plano-recorrencia > .content',
    CADASTRAR_PLANO_CREDITO: '#plano-credito > .content',
    CADASTRAR_PLANO_PERSONAL: '#plano-personal > .content',
    NOME: '#input-plano-nome',
    NOME_2: '[data-cy="input-plano-nome-input"]',
    ADICIONAR_LINHA: '[id^="table-renderer-0-add-row"]',
    ADICIONAR_LINHA_HORARIO: '#table-renderer-2-add-row',
    SELECT_PACOTE: '#select-table-duracao-plano-pacote',
    SELECT_MODALIDADE_NORMAL: '#select-table-duracao-plano-modalidade',
    SELECT_MODALIDADE: '#select-modalidade',
    SELECT_HORARIO: '#select-table-duracao-plano-horario',
    REPETICOES_NA_SEMANA: '#input-number-table-duracao-plano-vezesSemana'
};

export const NegociacaoPage = {
    // Cadastro aluno
    INCLUIR_CLIENTE: '#taskbar-resource-add-person > .pct',
    INPUT_CONSULTA: '#form\\:termosConsulta',
    BOTAO_CONSULTAR: '#form\\:btnConsultarCliente',
    BOTAO_CADASTRAR_NOVO: '#form\\:btnNovo',
    INPUT_DATA_NASCIMENTO: '#form\\:dataNascInputDate',
    MASCULINO: '#form\\:sexo\\:0',
    CONFIRMAR_VISITANTE: '#form\\:salvar',
    CONSULTOR_VISITATE: '#form\\:consultor',
    CADASTRAR_VISITANTE: '#form\\:cadastrarVisitante',
    CONFIRMAR_BV: '#formGravarBV\\:btnSimCadastrarVisitante',

    // Negociação
    BUSCA_RAPIDA: '[title="Busca rápida"]',
    INPUT_PLANO: '#input-plano',
    LISTA_CLIENTE: '.lista-cliente',
    SELECT_PLANO: '#input-plano',
    BTN_RECEBER: '#btn-receber',
    BTN_ENVIAR_LINK_PAGAMENTO: '#btn-enviar',
    INPUT_PASSWORD: '#aut-input-psw',
    BTN_CONFIRM: '.ng-star-inserted',
    SELECT_CONDICAO_PAGAMENTO: '#input-condicao',
    MODAL_SELECT_CONDICAO_PAGAMENTO: '[label="Condição de pagamento"]',
    INPUT_SENHA: '#aut-input-psw',
    BTN_CONFIRMAR_SENHA: '#aut-btn-confirm',
    CAMPO_DATA_INICIAL_CONTRATO: '.zebra-row > :nth-child(3)',
    CAMPO_DATA_FINAL_CONTRATO: '.zebra-row > :nth-child(4)',
    FECHAR_ABA: '.close-wrapper',
    FECHAR_MODAL: '#close-aba',

    // Elementos específicos
    ENVIAR_LINK_PAGAMENTO: '#btn-enviar > .content',
    SENHA: '#aut-input-psw',
    CONFIRMAR_SENHA: '#aut-btn-confirm',
    TABELA_PARCELAS_NEGOCIACAO: '.parcela-valor'
};

// ==================== MÓDULO AGENDA ====================

export const AgendaAulaPage = {
    DIA_ANTERIOR: '#dia-anterior',
    PROXIMO_DIA: '#proximo-dia',
    FILTRO_TIPO: '#filtro-tipo',
    LINHA_08HRS: ':nth-child(17) > .slot-dia',
    LINHA_01HRS: ':nth-child(3) > .slot-dia > .ng-star-inserted > #slot-card-disponivel > .visualizar > div',
    MODAL_SERVICO: '.table-content',
    INPUT_NOME_ALUNO: '#aluno-adicionar-agendamento',
    ALUNO_SELECIONADO: '#aluno-adicionar-agendamento-0',
    INPUT_DURACAO_MINUTOS: '.form-group > #duracao-adicionar-agendamento',
    SALVAR_AGENDAMENTO: '[data-cy="salvar-adicionar-agendamento"]',
    INPUT_BUSCAR_ALUNO: '#aluno-adicionar-agendamento-filter',
    MODAL_DISPONIBILIDADES: '.table-content',
    PAGE_SIZE: '#page-size-control',
    TABELA_AULAS: '.content-agenda',
    CONFIGURAR_AULA: '[data-cy="btn-redirect-aula"]',
    EXCLUIR: '[data-cy="btn-excluir-agendamento"] > .content',
    CONFIRMAR_EXCLUSAO: '[data-cy="btn-confirmar-exclusao-agendamento"] > .content'
};

export const AgendaServicoPage = {
    ADICIONAR_SERVICO: '#btn-novo-servico',
    NOME_SERVICO: '#nome-servico-input',
    VALOR_SERVICO: '#valor-servico-input',
    DURACAO_SERVICO: '#duracao-servico-input',
    SALVAR_SERVICO: '#btn-add-servico',
    BUSCA_RAPIDA: '#input-busca-rapida',
    EDITAR: '#element-0-edit',
    EXCLUIR: '#element-0-remove',
    REMOVER: '#action-remover'
};

export const AulasExcluidasPage = {
    EXCLUIR: '[id^="element-"][id$="-remove"]',
    LISTA_AULAS: '.table-content',
    REMOVER: '#action-remover'
};

export const ConfigurarAulaPage = {
    ADICIONAR_AULA: '#btn-nova-aula',
    NOME_AULA: '#nome-aula-input',
    MODALIDADE: '#modalidade-select',
    PROFESSOR: '#professor-select',
    AMBIENTE: '#ambiente-select',
    CAPACIDADE: '#capacidade-input',
    SALVAR_AULA: '#btn-add-aula',
    FIXAR_AULA: '#fixar-aula-check-input'
};

export const DisponibilidadeServicoPage = {
    NOME_SERVICO: '[data-cy="nome-disponibilidade-input-input"]',
    INTERVALO_FALTAS: '#intervalo-faltas-input',
    ADICIONAR_DISPONIBILIDADE: '#btn-add-disponibilidade',
    SALVAR_DISPONIBILIDADE: '#btn-save-disponibilidade'
};

export const LocacaoPage = {
    ADICIONAR_LOCACAO: '#btn-nova-locacao',
    CLIENTE: '#cliente-locacao-input',
    PRODUTO: '#produto-locacao-input',
    DATA_INICIO: '#data-inicio-locacao',
    DATA_FIM: '#data-fim-locacao',
    SALVAR_LOCACAO: '#btn-save-locacao'
};

export const TVAulaPage = {
    TELA_TV: '.tv-aula-container',
    PROXIMA_AULA: '.proxima-aula',
    AULA_ATUAL: '.aula-atual',
    LISTA_ALUNOS: '.lista-alunos-aula'
};

export const TVGestorPage = {
    DASHBOARD_TV: '.tv-gestor-container',
    METRICAS: '.metricas-tv',
    GRAFICOS: '.graficos-tv'
};

// ==================== MÓDULO TREINO ====================

export const TreinoHomePage = {
    SELECT_MODAL: 'ds3-select',
    OPTION_SELECT_MODAL: '.ds3-select-options',
    ICONE_CALENDARIO: '.avisos-modal-form .pct-calendar',
    DATA_ATUAL: '.mat-calendar-body-active > .mat-calendar-body-cell-content',
    INPUT_MENSAGEM: '.avisos-modal-form textarea',
    AVISOS: '.avisos',
    BOTAO_ACOES: '.avisos-item .dropdown-toggle',
    SELECT_PESSOAS: '.avisos-modal-form ds3-select-multi',
    TITULO_MODAL: '.modal-titulo'
};

export const ProgramaTreinoPage = {
    NOVA_FICHA: '#ficha-nova',
    EXCLUIR_FICHA: '#excluir-ficha > .content',
    OK_MODAL: '#action-ok',
    ABA_FICHAS: '.card-info-tabs-ficha',
    PRIMEIRA_FICHA: '.scroll-content > :nth-child(1)',
    VOLTAR: '.card-retorno-container > .pct',
    ENVIAR_TREINO: '#enviar-programa > .content',
    INPUT_BUSCA_ALUNO: 'input[title="Busque por aluno"]',
    ALUNO_SELECIONADO: '.zebra-row > .tdnome',
    BOTAO_ENVIAR_PROGRAMA: '#btn-enviar'
};

export const AtividadePage = {
    ADICIONAR_ATIVIDADE: '#btn-nova-atividade',
    NOME_ATIVIDADE: '#nome-atividade-input',
    GRUPO_MUSCULAR: '#grupo-muscular-select',
    TIPO_ATIVIDADE: '#tipo-atividade-select',
    SALVAR_ATIVIDADE: '#btn-add-atividade',
    BUSCA_RAPIDA: '#input-busca-rapida',
    EDITAR: '#element-0-edit',
    EXCLUIR: '#element-0-remove',
    REMOVER: '#action-remover'
};

export const AlunosFaltososPage = {
    FILTRO_DATA: '#filtro-data-faltas',
    FILTRO_MODALIDADE: '#filtro-modalidade-faltas',
    BUSCAR_FALTAS: '#btn-buscar-faltas',
    TABELA_FALTAS: '.table-faltas',
    EXPORTAR: '#btn-exportar-faltas'
};

export const PerfilAcessoTreinoPage = {
    ADICIONAR_PERFIL: '#btn-novo-perfil',
    NOME_PERFIL: '#nome-perfil-input',
    PERMISSOES: '.permissoes-container',
    SALVAR_PERFIL: '#btn-save-perfil',
    EDITAR_PERFIL: '#element-0-edit',
    EXCLUIR_PERFIL: '#element-0-remove'
};

export const PrescricaoTreinoPage = {
    SELECIONAR_ALUNO: '#select-aluno-prescricao',
    ADICIONAR_EXERCICIO: '#btn-add-exercicio',
    NOME_EXERCICIO: '#nome-exercicio-input',
    SERIES: '#series-input',
    REPETICOES: '#repeticoes-input',
    CARGA: '#carga-input',
    SALVAR_PRESCRICAO: '#btn-save-prescricao'
};

export const TreinoPorIAPage = {
    GERAR_TREINO_IA: '#btn-gerar-treino-ia',
    OBJETIVO_TREINO: '#objetivo-treino-select',
    NIVEL_EXPERIENCIA: '#nivel-experiencia-select',
    TEMPO_DISPONIVEL: '#tempo-disponivel-input',
    EQUIPAMENTOS: '#equipamentos-select',
    GERAR: '#btn-gerar-ia',
    TREINO_GERADO: '.treino-ia-container',
    ACEITAR_TREINO: '#btn-aceitar-treino-ia',
    REJEITAR_TREINO: '#btn-rejeitar-treino-ia'
};

// ==================== MÓDULO AVALIAÇÃO FÍSICA ====================

export const AnamnesePage = {
    ADICIONAR: '#btn-novo-anamnese',
    NOME_ANAMNESE: '#nome-anamnese-input',
    ADICIONAR_PERGUNTA: '#btn-add-pergunta',
    DESCRICAO_PRIMEIRA_PERGUNTA: '#descricao-pergunta-0',
    TIPO_PERGUNTA: '#tipo-pergunta-select-0',
    PERGUNTA_ADICIONADA: ':nth-child(1) > .anamnese-pergunta-wrapper',
    ADICIONAR_OPCAO: '#add-opcao-check-0',
    PRIMEIRA_OPCAO: '.option > pacto-inline-text > .form-group > .form-control',
    SEGUNDA_OPCAO: '.options-wrapper > :nth-child(2) > pacto-inline-text > .form-group > .form-control',
    CRIAR_ANAMNESE: '#btn-add-anamnese',
    ANAMNESE_LANCADA: ':nth-child(1) > .column-cell',
    EXCLUIR: '#element-0-remove',
    REMOVER: '#action-remover'
};

export const AvaliacaoAlunoPage = {
    SELECIONAR_ALUNO: '#select-aluno-avaliacao',
    PESO: '#peso-input',
    ALTURA: '#altura-input',
    PERCENTUAL_GORDURA: '#percentual-gordura-input',
    MASSA_MUSCULAR: '#massa-muscular-input',
    OBSERVACOES: '#observacoes-textarea',
    SALVAR_AVALIACAO: '#btn-save-avaliacao',
    HISTORICO_AVALIACOES: '.historico-avaliacoes',
    NOVA_AVALIACAO: '#btn-nova-avaliacao'
};

// ==================== MÓDULO CONFIGURAÇÕES ====================

export const ConfiguracoesPage = {
    ENGRENAGEM: '#taskbar-bottom-nav-config',
    TREINO: '#sidebar-item-config-treino',
    AULAS: '.config-wrapper > .tabs > :nth-child(2)',
    BLOQUEAR_CHECKIN_PAGAMENTO_PRIMEIRA_PARCELA: '#check-proibir_marcar_aula_antes_pagamento_primeira_parcela',
    SALVAR: '#btn-save-config-aula',
    MENU_TREINO: '#sidebar-item-config-treino > .pacto-item-menu > .pacto-item-menu-content',
    INTELIGENCIA_ARTIFICIAL: '.tabs > :nth-child(9)',
    PERMITIR_CRIACAO_IA: '#check-permitir_criar_treino_automatizado_ia',
    PERMITIR_ALUNO_CRIAR_IA: '#check-permitir_aluno_criar_treino_ia_app',
    BOTAO_SALVAR_TREINO: '#btn-save-config-treino',
    EXPANDIR_MENU: '#sidebar-menu-toggle > .pct'
};

export const ConfiguracaoPage = {
    MENU_FINANCEIRO: '#sidebar-item-config-fin',
    PREENCHIMENTO_MANUAL_COMPETENCIA: '#form\\:obrigarPreenchimentoManualDtCompetencia',
    GRAVAR: '#form\\:novo',
    CRIAR_CONTA_LANCAR_COMPRA: '#form\\:contaPagarCompraEstoque'
};

// ==================== MÓDULO PACTOPAY ====================

export const BusinessIntelligencePage = {
    TITULO: '.titulo'
};

export const CartaoDeCreditoPage = {
    TITULO: '.pct-page-title',
    ADICIONAR_CARTAO: '#btn-add-cartao',
    NUMERO_CARTAO: '#numero-cartao-input',
    NOME_TITULAR: '#nome-titular-input',
    VALIDADE: '#validade-input',
    CVV: '#cvv-input',
    SALVAR_CARTAO: '#btn-save-cartao'
};

export const ParcelasEmAbertoPage = {
    TITULO: '.titulo',
    FILTRO_CLIENTE: '#filtro-cliente',
    FILTRO_DATA: '#filtro-data',
    BUSCAR_PARCELAS: '#btn-buscar-parcelas',
    TABELA_PARCELAS: '.table-parcelas',
    RECEBER_PARCELA: '#btn-receber-parcela'
};

export const PixPage = {
    TITULO: '.pct-page-title',
    GERAR_PIX: '#btn-gerar-pix',
    VALOR_PIX: '#valor-pix-input',
    DESCRICAO_PIX: '#descricao-pix-input',
    QR_CODE: '.qr-code-container',
    COPIAR_CODIGO: '#btn-copiar-codigo-pix'
};

export const TransacoesCartaoDeCreditoPage = {
    TITULO: '.pct-page-title',
    FILTRO_PERIODO: '#filtro-periodo',
    FILTRO_STATUS: '#filtro-status',
    BUSCAR_TRANSACOES: '#btn-buscar-transacoes',
    TABELA_TRANSACOES: '.table-transacoes',
    DETALHES_TRANSACAO: '#btn-detalhes-transacao'
};

// ==================== MÓDULO CROSS ====================

export const TipoBenchmarkPage = {
    ADICIONAR_TIPO: '#btn-novo-tipo-benchmark',
    NOME_TIPO: '#nome-tipo-input',
    DESCRICAO_TIPO: '#descricao-tipo-input',
    SALVAR_TIPO: '#btn-save-tipo',
    BUSCA_RAPIDA: '#input-busca-rapida',
    EDITAR: '#element-0-edit',
    EXCLUIR: '#element-0-remove',
    REMOVER: '#action-remover'
};

export const WODPage = {
    ADICIONAR_WOD: '#btn-add-wod',
    NOME_WOD: '[id="\'nome-wod-input\'"] input',
    TIPO_WOD: '.card-wrapper > :nth-child(3) > :nth-child(1) > :nth-child(2) .ds3-select',
    CALENDARIO: '.card-wrapper > :nth-child(3) > :nth-child(2) > :nth-child(2) button',
    CALENDARIO_DIA_ATUAL: '.mat-calendar-body-active > .mat-calendar-body-cell-content',
    WOD: 'textarea[placeholder="Descreva aqui os wod..."]',
    SALVAR: '#btnSalvar',
    BUSCA_RAPIDA: '#input-busca-rapida',
    EDITAR: '#element-0-edit',
    EXCLUIR: '#element-0-remove',
    REMOVER: '#action-remover',
    SELECT_ANGULAR: 'ds3-select',
    SELECT_OPTIONS: '.ds3-select-options'
};

// ==================== MÓDULO MENU LATERAL ====================

export const MenuLateralPage = {
    ICONE_CONFIGURACAO: '#taskbar-bottom-nav-config > .pct',
    TELA_INTEGRACAO: '#sidebar-item-config-integracoes > .pacto-item-menu > .pacto-item-menu-content',
    TELA_TREINO: '#sidebar-item-config-treino > .pacto-item-menu > .pacto-item-menu-content',
    BOTAO_AVANCAR: '#btn-avancar > .content',
    ADM: '#ADM',
    CRM: '#CRM',
    FIN: '#FINANCEIRO',
    TREINO: '#TREINO',
    PAY: '#PACTO_PAY',
    LOG_TREINO: '#show-log',
    FECHAR_LOG: '.close > span'
};

// ==================== MÓDULO MENU SUPERIOR ====================

export const QRCodePage = {
    MENU_QRCODE: '.pct-qr-code',
    ASSINATURA_DIGITAL: '.qr-code-options > :nth-child(1) > span',
    VACINA: '.qr-code-options > :nth-child(2) > span',
    PARQ: '.qr-code-options > :nth-child(3) > span',
    TEXTO: '.text-acesso > span',
    QRCODE: '.img-qrcode > img',
    BOTAO_FECHAR: '#close-aba'
};

// ==================== MÓDULO NOVO CAIXA ABERTO ====================

export const NovoCaixaAbertoPage = {
    INPUT_BUSCA_RAPIDA: '#input-busca-rapida',
    TABELA_CLIENTE: '.table-content',
    CHECK_TODAS_PARCELAS_ALUNO: '#ds3-checkbox__custom-table-row-0-cell-cliente-checkbox-group',
    SELECT_CONVENIO: 'ds3-select[valuekey="codigo"]',
    DS3_SELECT_OPTIONS: '.ds3-select-options',
    BOTAO_RECEBER: '#caixa-em-aberto-btn-receber',
    BOTAO_RECEBER_CAIXA_ABERTO: '#caixa-em-aberto-btn-receber',
    SELECT_FORMA_PAGAMENTO: 'ds3-select[placeholder="Selecione a forma de pagamento"]',
    MODAL_FINALIZAR: '#finalizar',
    SELECIONAR_FORMA_PAGAMENTO: 'span.ds3-select-value-placeholder',
    SELECIONAR_ADQUIRENTE: '#carta-credito-offline-0-select-adquirente > .ds3-select',
    INPUT_VALOR_RECEBIDO: '#input-valor-recebido',
    CONFIRMAR_RECEBIMENTO: '#btn-confirmar-recebimento'
};

// ==================== MÓDULO VENDAS ONLINE ====================

export const CadastroCartaoPage = {
    NOME_TITULAR: '#idnomecartao',
    NUMERO_CARTAO: '#idnrcartao',
    VALIDADE: '#idvalidade',
    CODIGO_SEGURANCA: '#idcvv',
    CHECK_LGPD: ':nth-child(5) > .col-12 > pacto-input > .form-group > .form-control',
    ADICIONAR_CARTAO: '.pacto-btn-primary'
};