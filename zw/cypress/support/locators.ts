/**
 * Locators centralizados para as páginas do sistema
 * Organizados por funcionalidade para facilitar manutenção
 */

export const CadastroClientePage = {
    // Ações principais
    ADICIONAR_CLIENTE: '#taskbar-resource-add-person',
    CONSULTAR_CEP: '#add-cliente-cadastro-cep-buscar',
    BOTAO_CONSULTAR_CLIENTE: '#add-cliente-btn-consultar',
    PROSSEGUIR: '#btn-inf-cadastro-prosseguir',
    SALVAR_VISITANTE: '#add-cliente-salvar-visitante',
    REALIZAR_NEGOCIACAO: '#add-cliente-realizar-negociacao',
    TRANSFERIR_CLIENTE: '[data-cy="btn-mdl-transferir-cliente-empresa-transferir"] > .content',

    // Campos de dados pessoais
    CPF: '#add-cliente-cpf',
    NOME: '#add-cliente-cadastro-nome',
    RG: '#add-cliente-cadastro-rg',
    DATA_NASCIMENTO: '#ds3-btn-26 > .pct',
    TELEFONE: '#add-cliente-cadastro-telefone-input',
    EMAIL: '#add-cliente-cadastro-email',
    SEXO: '#add-cliente-cadastro-sexo > .ds3-select',
    PROFISSAO: '.icon-prof > .pct',

    // Campos de endereço
    CEP: '#add-cliente-cadastro-cep',
    ENDERECO: '#add-cliente-cadastro-endereco',

    // Campos de negociação e consultor
    CONSULTOR: '#add-cliente-boletim-consultor > .ds3-select',

    // Campos de autenticação
    SENHA: '#aut-input-psw',
    CONFIRMAR_SENHA: '#aut-btn-confirm',
    ENVIAR_LINK: '#btn-enviar > .content',

    // Campos de busca
    CAMPO_NOME_BUSCA: '#add-cliente-nome',
    CLIENTE: '.nome-cliente'
};

export const TelaDoClientePage = {
    BUSCA_GERAL: '#topbar-search-field',
    RESULTADO_BUSCA_GERAL: '#cat-autocomplete-0',
    PLANO_ALUNO: ':nth-child(4) > .column-cell > .ng-star-inserted',
    VALOR_PLANO: ':nth-child(6) > .detail-data-value > span',
    ABA_FINANCEIRO: '#pcc-tab-financeiro',
    PRIMEIRO_REGISTRO_COMPRAS: '#element-0-pch-fin-table-compras',
    PAGINADOR_PARCELAS: '#page-size-control-tbl-prod-cobrados-parcela',
    MODALIDADES: '.title-modalidade-turma',
    LISTA_TURMAS: '.modalidade-data',
    ABA_TREINO: '#pcc-tab-treino',
    CRIAR_PROGRAMA: '#renovar > .content',
    CRIAR_UM_NOVO: '#button-criar-novo-programa > .content',
    FECHAR_MODAL: '#close-aba',
    PROGRAMA_ATUAL: '#tr-btn-modal-programa',
    EXCLUIR_PROGRAMA: '#element-0-remove-tr-tbl-lista-programa',
    REMOVER: '#action-remover'
};

export const PaisPage = {
    ADICIONAR: '#btn-add-item',
    NOME: 'input[placeholder="Brasil"]',
    NACIONALIDADE: 'input[placeholder="Brasileira(o)"]',
    SIGLA: 'input[placeholder="GO"]',
    ESTADO: 'input[placeholder="Goiás"]',
    ADICIONAR_ESTADO: '.add-estado > span',
    BUSCA: '#input-busca-rapida',
    LISTA: '#element-0 > :nth-child(2)',
    EXCLUIR: '#element-0-action-delete\\ \\(key\\)'
};

export const CidadePage = {
    ADICIONAR: '#btn-add-item',
    NOME: '[title="Nome"]',
    PAIS: '.current-value',
    ESTADO: '.current-wrapper .option-label',
    FILTRO: '[placeholder="Filtro..."]',
    BUSCA: '#input-busca-rapida',
    EXCLUIR: '#element-0-action-delete\\ \\(key\\)'
};

export const BancoPage = {
    ADICIONAR: '#btn-add-item',
    NOME: '[title="Banco"]',
    CODIGO: '.ng-untouched.ng-pristine.ng-invalid',
    BUSCA: '#input-busca-rapida',
    EDITAR: '#element-0-editbanco',
    EXCLUIR: '#element-0-deletebanco'
};

export const ModalidadePage = {
    DESCRICAO: 'input[formcontrolname="nome"]',
    VALOR_MENSAL: 'input[formcontrolname="valorMensal"]',
    VEZES_NA_SEMANA: 'input[id="-input"]',
    BUSCA_RAPIDA: '#input-busca-rapida',
    VOLTAR: '#detalhamento-contrato-voltar',
    PRIMEIRA_LIXEIRA: '#element-0-excluir'
};

export const PerguntaPage = {
    ADICIONAR_PERGUNTA: '#btn-add-item',
    DESCRICAO: 'input[placeholder="Descrição"]',
    RESPOSTA: 'input[placeholder="resposta"]',
    ADICIONAR: '.add-resposta',
    EDITAR: 'i.pct-edit',
    BUSCA: '#input-busca-rapida',
    EXCLUIR: '#element-0-action-delete\\ \\(key\\)',
    TABELA_PERGUNTAS: '.table-content'
};

export const CampanhaPage = {
    CADASTRAR_CAMPANHA: '#form\\:btnCadastrarCampanha',
    NOME: '#formEditar\\:nomeBrinde',
    DATA_INICIO: '#formEditar\\:dataInicialInputDate',
    DATA_FIM: '#formEditar\\:dataFinalInputDate',
    DESCRICAO: '#formEditar\\:descricao',
    CATEGORIA: '#formEditar\\:tblComPontos\\:0\\:multipicadorCategoria',
    SALVAR: '#formEditar\\:salvar',
    EDITAR: '#form\\:listaCampanha\\:0\\:btnEditar',
    EXCLUIR: '#form\\:listaCampanha\\:0\\:btnExcluir1',
    SIM: '#formAviso\\:confirmacaoExclusaoCampanha'
};

export const ConvenioDescontoPage = {
    ADICIONAR: '#btn-add-item',
    BUSCA: '#input-busca-rapida',
    DESCRICAO: 'input[title="Descrição"]',
    VIGENCIA_FINAL: '[data-cy="cat-datepicker-2-input"]',
    ADICIONAR_LINHA: '#table-renderer-0-add-row',
    DESCRICAO_DURACAO: '#input-number-duracao',
    TIPO_DESCONTO: '#select-tipoDesconto',
    VALOR: '#input-decimal-valorDesconto'
};

export const PessoasPage = {
    PESSOAS: '#taskbar-resource-pessoas > .pct',
    EMPRESA: '#element-0 > :nth-child(4) > .column-cell > .empresaCell > :nth-child(1)'
};

export const HomeAdmPage = {
    BOTAO_ADICIONAR_PESSOA: '#taskbar-resource-add-person > .pct',
    TROCAR_UNIDADE: '#menu-trocar-unidade',
    MODAL_TROCAR_UNIDADE: '.change-empesa-wrapper',
    NOME_EMPRESA: '.current-company',
    BOTAO_CONFIGURACAO: '#taskbar-bottom-nav-config',
    BOTAO_ALUNOS_FAVORITOS: '#topbar-alunos-favoritos > img',
    PRIMEIRO_ALUNO_RECENTE: ':nth-child(4) > :nth-child(1) > .col-12 > .row > .col-9 > .aluno-info > .line1 > a > .nomeAluno'
};

export const DescontoPage = {
    ADICIONAR: '#btn-add-plano-desconto',
    DESCRICAO: '[data-cy="plano-novo-desconto-descricao-input"]',
    TIPO_PRODUTO: '[data-cy="plano-novo-desconto-tipo-produto"]',
    ATIVO: '#plano-novo-desconto-ativo > .cat-input-wrapper > label > .checkmark-outline',
    TIPO_DESCONTO: '.aux-parent > #plano-novo-desconto-tipo-desconto',
    VALOR: '.aux-wrapper > #plano-novo-desconto-tipo-desconto-valor'
};

export const AmbientePage = {
    CADASTRAR_NOVO: '#form\\:btnNovo',
    DESCRICAO_AMBIENTE: '#form\\:descricao',
    CAPACIDADE: '#form\\:capacidade',
    TIPO_AMBIENTE: '#form\\:tiposAmbiente',
    COLETOR: '#form\\:coletor',
    SITUACAO: '#form\\:situacao',
    SALVAR: '#form\\:salvar',
    VOLTAR: '#form\\:consultar'
};

export const ProdutoPage = {
    BUSCAR_PRODUTO: '[class="fa-icon-search searchbox-icon"]',
    RESULTADO_BUSCA: '.odd > :nth-child(2)',
    PRE_PAGO: '#form\\:valorPacote',
    POS_PAGO: '#form\\:valorPacotePos',
    SALVAR: '#form\\:salvar',
    MENSAGEM: '#form\\:msgProduto',
    NOVO: '#form\\:btnNovo',
    DESCRICAO: '#form\\:descricao',
    TIPO_PRODUTO: '#form\\:tipoProduto',
    CATEGORIA: '#form\\:categoriaProduto',
    VALOR: '#form\\:valor',
    EMPRESA: '#form\\:empresaCfg',
    VALOR_EMPRESA: '#form\\:valorempresaCfg',
    ADD_EMPRESA: '#form\\:addempresaCfg'
};

export const SorteioPage = {
    BOTAO_TODOS: '.check-selecionar-todos > pacto-cat-checkbox > .cat-input-wrapper > label > .checkmark-outline',
    BOTAO_CONFIG_REALIZARSORTEIO: '[class="ng-star-inserted"]',
    BOTAO_SALVAR_VOLTAR: '[class="row justify-content-end custom-buttons"]',
    BOTAO_VOLTAR_REVELAR: '[class="row justify-content-center custom-btns"]',
    BOTAO_VOLTAR_VALIDAR: '[class="row custom-btns"]'
};

export const MenuSuperiorPage = {
    // Esta page não tem elements fixos, usa seletores dinâmicos
    EXPLORAR_TOGGLE: '#topbar-modules-toggle'
};

export const PlanoPage = {
    ADICIONAR: '#btn-add-plano',
    CADASTRAR_PLANO_NORMAL: '#plano-normal',
    CADASTRAR_PLANO_RECORRENTE: '#plano-recorrencia > .content',
    CADASTRAR_PLANO_CREDITO: '#plano-credito > .content',
    CADASTRAR_PLANO_PERSONAL: '#plano-personal > .content',
    NOME: '#input-plano-nome',
    NOME_2: '[data-cy="input-plano-nome-input"]',
    ADICIONAR_LINHA: '[id^="table-renderer-0-add-row"]',
    ADICIONAR_LINHA_HORARIO: '#table-renderer-2-add-row',
    SELECT_PACOTE: '#select-table-duracao-plano-pacote',
    SELECT_MODALIDADE_NORMAL: '#select-table-duracao-plano-modalidade',
    SELECT_MODALIDADE: '#select-modalidade',
    SELECT_HORARIO: '#select-table-duracao-plano-horario',
    REPETICOES_NA_SEMANA: '#input-number-table-duracao-plano-vezesSemana'
};

export const NegociacaoPage = {
    // Cadastro aluno
    INCLUIR_CLIENTE: '#taskbar-resource-add-person > .pct',
    INPUT_CONSULTA: '#form\\:termosConsulta',
    BOTAO_CONSULTAR: '#form\\:btnConsultarCliente',
    BOTAO_CADASTRAR_NOVO: '#form\\:btnNovo',
    INPUT_DATA_NASCIMENTO: '#form\\:dataNascInputDate',
    MASCULINO: '#form\\:sexo\\:0',
    CONFIRMAR_VISITANTE: '#form\\:salvar',
    CONSULTOR_VISITATE: '#form\\:consultor',
    CADASTRAR_VISITANTE: '#form\\:cadastrarVisitante',
    CONFIRMAR_BV: '#formGravarBV\\:btnSimCadastrarVisitante',

    // Negociação
    BUSCA_RAPIDA: '[title="Busca rápida"]',
    INPUT_PLANO: '#input-plano',
    LISTA_CLIENTE: '.lista-cliente',
    SELECT_PLANO: '#input-plano',
    BTN_RECEBER: '#btn-receber',
    BTN_ENVIAR_LINK_PAGAMENTO: '#btn-enviar',
    INPUT_PASSWORD: '#aut-input-psw',
    BTN_CONFIRM: '.ng-star-inserted',
    SELECT_CONDICAO_PAGAMENTO: '#input-condicao',
    MODAL_SELECT_CONDICAO_PAGAMENTO: '[label="Condição de pagamento"]',
    INPUT_SENHA: '#aut-input-psw',
    BTN_CONFIRMAR_SENHA: '#aut-btn-confirm',
    CAMPO_DATA_INICIAL_CONTRATO: '.zebra-row > :nth-child(3)',
    CAMPO_DATA_FINAL_CONTRATO: '.zebra-row > :nth-child(4)',
    FECHAR_ABA: '.close-wrapper',
    FECHAR_MODAL: '#close-aba',

    // Elementos específicos
    ENVIAR_LINK_PAGAMENTO: '#btn-enviar > .content',
    SENHA: '#aut-input-psw',
    CONFIRMAR_SENHA: '#aut-btn-confirm',
    TABELA_PARCELAS_NEGOCIACAO: '.parcela-valor'
};

// ==================== MÓDULO AGENDA ====================

export const AgendaAulaPage = {
    DIA_ANTERIOR: '#dia-anterior',
    PROXIMO_DIA: '#proximo-dia',
    FILTRO_TIPO: '#filtro-tipo',
    LINHA_08HRS: ':nth-child(17) > .slot-dia',
    LINHA_01HRS: ':nth-child(3) > .slot-dia > .ng-star-inserted > #slot-card-disponivel > .visualizar > div',
    MODAL_SERVICO: '.table-content',
    INPUT_NOME_ALUNO: '#aluno-adicionar-agendamento',
    ALUNO_SELECIONADO: '#aluno-adicionar-agendamento-0',
    INPUT_DURACAO_MINUTOS: '.form-group > #duracao-adicionar-agendamento',
    SALVAR_AGENDAMENTO: '[data-cy="salvar-adicionar-agendamento"]',
    INPUT_BUSCAR_ALUNO: '#aluno-adicionar-agendamento-filter',
    MODAL_DISPONIBILIDADES: '.table-content',
    PAGE_SIZE: '#page-size-control',
    TABELA_AULAS: '.content-agenda',
    CONFIGURAR_AULA: '[data-cy="btn-redirect-aula"]',
    EXCLUIR: '[data-cy="btn-excluir-agendamento"] > .content',
    CONFIRMAR_EXCLUSAO: '[data-cy="btn-confirmar-exclusao-agendamento"] > .content'
};

export const AgendaServicoPage = {
    ADICIONAR_SERVICO: '#btn-novo-servico',
    NOME_SERVICO: '#nome-servico-input',
    VALOR_SERVICO: '#valor-servico-input',
    DURACAO_SERVICO: '#duracao-servico-input',
    SALVAR_SERVICO: '#btn-add-servico',
    BUSCA_RAPIDA: '#input-busca-rapida',
    EDITAR: '#element-0-edit',
    EXCLUIR: '#element-0-remove',
    REMOVER: '#action-remover'
};

export const AulasExcluidasPage = {
    EXCLUIR: '[id^="element-"][id$="-remove"]',
    LISTA_AULAS: '.table-content',
    REMOVER: '#action-remover'
};

export const ConfigurarAulaPage = {
    ADICIONAR_AULA: '#btn-nova-aula',
    NOME_AULA: '#nome-aula-input',
    MODALIDADE: '#modalidade-select',
    PROFESSOR: '#professor-select',
    AMBIENTE: '#ambiente-select',
    CAPACIDADE: '#capacidade-input',
    SALVAR_AULA: '#btn-add-aula',
    FIXAR_AULA: '#fixar-aula-check-input'
};

export const DisponibilidadeServicoPage = {
    NOME_SERVICO: '[data-cy="nome-disponibilidade-input-input"]',
    INTERVALO_FALTAS: '#intervalo-faltas-input',
    ADICIONAR_DISPONIBILIDADE: '#btn-add-disponibilidade',
    SALVAR_DISPONIBILIDADE: '#btn-save-disponibilidade'
};

export const LocacaoPage = {
    ADICIONAR_LOCACAO: '#btn-nova-locacao',
    CLIENTE: '#cliente-locacao-input',
    PRODUTO: '#produto-locacao-input',
    DATA_INICIO: '#data-inicio-locacao',
    DATA_FIM: '#data-fim-locacao',
    SALVAR_LOCACAO: '#btn-save-locacao'
};

export const TVAulaPage = {
    TELA_TV: '.tv-aula-container',
    PROXIMA_AULA: '.proxima-aula',
    AULA_ATUAL: '.aula-atual',
    LISTA_ALUNOS: '.lista-alunos-aula'
};

export const TVGestorPage = {
    DASHBOARD_TV: '.tv-gestor-container',
    METRICAS: '.metricas-tv',
    GRAFICOS: '.graficos-tv'
};

// ==================== MÓDULO TREINO ====================

export const TreinoHomePage = {
    SELECT_MODAL: 'ds3-select',
    OPTION_SELECT_MODAL: '.ds3-select-options',
    ICONE_CALENDARIO: '.avisos-modal-form .pct-calendar',
    DATA_ATUAL: '.mat-calendar-body-active > .mat-calendar-body-cell-content',
    INPUT_MENSAGEM: '.avisos-modal-form textarea',
    AVISOS: '.avisos',
    BOTAO_ACOES: '.avisos-item .dropdown-toggle',
    SELECT_PESSOAS: '.avisos-modal-form ds3-select-multi',
    TITULO_MODAL: '.modal-titulo'
};

export const ProgramaTreinoPage = {
    NOVA_FICHA: '#ficha-nova',
    EXCLUIR_FICHA: '#excluir-ficha > .content',
    OK_MODAL: '#action-ok',
    ABA_FICHAS: '.card-info-tabs-ficha',
    PRIMEIRA_FICHA: '.scroll-content > :nth-child(1)',
    VOLTAR: '.card-retorno-container > .pct',
    ENVIAR_TREINO: '#enviar-programa > .content',
    INPUT_BUSCA_ALUNO: 'input[title="Busque por aluno"]',
    ALUNO_SELECIONADO: '.zebra-row > .tdnome',
    BOTAO_ENVIAR_PROGRAMA: '#btn-enviar'
};

export const AtividadePage = {
    ADICIONAR_ATIVIDADE: '#btn-nova-atividade',
    NOME_ATIVIDADE: '#nome-atividade-input',
    GRUPO_MUSCULAR: '#grupo-muscular-select',
    TIPO_ATIVIDADE: '#tipo-atividade-select',
    SALVAR_ATIVIDADE: '#btn-add-atividade',
    BUSCA_RAPIDA: '#input-busca-rapida',
    EDITAR: '#element-0-edit',
    EXCLUIR: '#element-0-remove',
    REMOVER: '#action-remover'
};

export const AlunosFaltososPage = {
    FILTRO_DATA: '#filtro-data-faltas',
    FILTRO_MODALIDADE: '#filtro-modalidade-faltas',
    BUSCAR_FALTAS: '#btn-buscar-faltas',
    TABELA_FALTAS: '.table-faltas',
    EXPORTAR: '#btn-exportar-faltas'
};

export const PerfilAcessoTreinoPage = {
    ADICIONAR_PERFIL: '#btn-novo-perfil',
    NOME_PERFIL: '#nome-perfil-input',
    PERMISSOES: '.permissoes-container',
    SALVAR_PERFIL: '#btn-save-perfil',
    EDITAR_PERFIL: '#element-0-edit',
    EXCLUIR_PERFIL: '#element-0-remove'
};

export const PrescricaoTreinoPage = {
    SELECIONAR_ALUNO: '#select-aluno-prescricao',
    ADICIONAR_EXERCICIO: '#btn-add-exercicio',
    NOME_EXERCICIO: '#nome-exercicio-input',
    SERIES: '#series-input',
    REPETICOES: '#repeticoes-input',
    CARGA: '#carga-input',
    SALVAR_PRESCRICAO: '#btn-save-prescricao'
};

export const TreinoPorIAPage = {
    GERAR_TREINO_IA: '#btn-gerar-treino-ia',
    OBJETIVO_TREINO: '#objetivo-treino-select',
    NIVEL_EXPERIENCIA: '#nivel-experiencia-select',
    TEMPO_DISPONIVEL: '#tempo-disponivel-input',
    EQUIPAMENTOS: '#equipamentos-select',
    GERAR: '#btn-gerar-ia',
    TREINO_GERADO: '.treino-ia-container',
    ACEITAR_TREINO: '#btn-aceitar-treino-ia',
    REJEITAR_TREINO: '#btn-rejeitar-treino-ia'
};

// ==================== MÓDULO AVALIAÇÃO FÍSICA ====================

export const AnamnesePage = {
    ADICIONAR: '#btn-novo-anamnese',
    NOME_ANAMNESE: '#nome-anamnese-input',
    ADICIONAR_PERGUNTA: '#btn-add-pergunta',
    DESCRICAO_PRIMEIRA_PERGUNTA: '#descricao-pergunta-0',
    TIPO_PERGUNTA: '#tipo-pergunta-select-0',
    PERGUNTA_ADICIONADA: ':nth-child(1) > .anamnese-pergunta-wrapper',
    ADICIONAR_OPCAO: '#add-opcao-check-0',
    PRIMEIRA_OPCAO: '.option > pacto-inline-text > .form-group > .form-control',
    SEGUNDA_OPCAO: '.options-wrapper > :nth-child(2) > pacto-inline-text > .form-group > .form-control',
    CRIAR_ANAMNESE: '#btn-add-anamnese',
    ANAMNESE_LANCADA: ':nth-child(1) > .column-cell',
    EXCLUIR: '#element-0-remove',
    REMOVER: '#action-remover'
};

export const AvaliacaoAlunoPage = {
    SELECIONAR_ALUNO: '#select-aluno-avaliacao',
    PESO: '#peso-input',
    ALTURA: '#altura-input',
    PERCENTUAL_GORDURA: '#percentual-gordura-input',
    MASSA_MUSCULAR: '#massa-muscular-input',
    OBSERVACOES: '#observacoes-textarea',
    SALVAR_AVALIACAO: '#btn-save-avaliacao',
    HISTORICO_AVALIACOES: '.historico-avaliacoes',
    NOVA_AVALIACAO: '#btn-nova-avaliacao'
};

// ==================== MÓDULO CONFIGURAÇÕES ====================

export const ConfiguracoesPage = {
    ENGRENAGEM: '#taskbar-bottom-nav-config',
    TREINO: '#sidebar-item-config-treino',
    AULAS: '.config-wrapper > .tabs > :nth-child(2)',
    BLOQUEAR_CHECKIN_PAGAMENTO_PRIMEIRA_PARCELA: '#check-proibir_marcar_aula_antes_pagamento_primeira_parcela',
    SALVAR: '#btn-save-config-aula',
    MENU_TREINO: '#sidebar-item-config-treino > .pacto-item-menu > .pacto-item-menu-content',
    INTELIGENCIA_ARTIFICIAL: '.tabs > :nth-child(9)',
    PERMITIR_CRIACAO_IA: '#check-permitir_criar_treino_automatizado_ia',
    PERMITIR_ALUNO_CRIAR_IA: '#check-permitir_aluno_criar_treino_ia_app',
    BOTAO_SALVAR_TREINO: '#btn-save-config-treino',
    EXPANDIR_MENU: '#sidebar-menu-toggle > .pct'
};

export const ConfiguracaoPage = {
    MENU_FINANCEIRO: '#sidebar-item-config-fin',
    PREENCHIMENTO_MANUAL_COMPETENCIA: '#form\\:obrigarPreenchimentoManualDtCompetencia',
    GRAVAR: '#form\\:novo',
    CRIAR_CONTA_LANCAR_COMPRA: '#form\\:contaPagarCompraEstoque'
};

// ==================== MÓDULO PACTOPAY ====================

export const BusinessIntelligencePage = {
    TITULO: '.titulo'
};

export const CartaoDeCreditoPage = {
    TITULO: '.pct-page-title',
    ADICIONAR_CARTAO: '#btn-add-cartao',
    NUMERO_CARTAO: '#numero-cartao-input',
    NOME_TITULAR: '#nome-titular-input',
    VALIDADE: '#validade-input',
    CVV: '#cvv-input',
    SALVAR_CARTAO: '#btn-save-cartao'
};

export const ParcelasEmAbertoPage = {
    TITULO: '.titulo',
    FILTRO_CLIENTE: '#filtro-cliente',
    FILTRO_DATA: '#filtro-data',
    BUSCAR_PARCELAS: '#btn-buscar-parcelas',
    TABELA_PARCELAS: '.table-parcelas',
    RECEBER_PARCELA: '#btn-receber-parcela'
};

export const PixPage = {
    TITULO: '.pct-page-title',
    GERAR_PIX: '#btn-gerar-pix',
    VALOR_PIX: '#valor-pix-input',
    DESCRICAO_PIX: '#descricao-pix-input',
    QR_CODE: '.qr-code-container',
    COPIAR_CODIGO: '#btn-copiar-codigo-pix'
};

export const TransacoesCartaoDeCreditoPage = {
    TITULO: '.pct-page-title',
    FILTRO_PERIODO: '#filtro-periodo',
    FILTRO_STATUS: '#filtro-status',
    BUSCAR_TRANSACOES: '#btn-buscar-transacoes',
    TABELA_TRANSACOES: '.table-transacoes',
    DETALHES_TRANSACAO: '#btn-detalhes-transacao'
};

// ==================== MÓDULO CROSS ====================

export const TipoBenchmarkPage = {
    ADICIONAR_TIPO: '#btn-novo-tipo-benchmark',
    NOME_TIPO: '#nome-tipo-input',
    DESCRICAO_TIPO: '#descricao-tipo-input',
    SALVAR_TIPO: '#btn-save-tipo',
    BUSCA_RAPIDA: '#input-busca-rapida',
    EDITAR: '#element-0-edit',
    EXCLUIR: '#element-0-remove',
    REMOVER: '#action-remover'
};

export const WODPage = {
    ADICIONAR_WOD: '#btn-add-wod',
    NOME_WOD: '[id="\'nome-wod-input\'"] input',
    TIPO_WOD: '.card-wrapper > :nth-child(3) > :nth-child(1) > :nth-child(2) .ds3-select',
    CALENDARIO: '.card-wrapper > :nth-child(3) > :nth-child(2) > :nth-child(2) button',
    CALENDARIO_DIA_ATUAL: '.mat-calendar-body-active > .mat-calendar-body-cell-content',
    WOD: 'textarea[placeholder="Descreva aqui os wod..."]',
    SALVAR: '#btnSalvar',
    BUSCA_RAPIDA: '#input-busca-rapida',
    EDITAR: '#element-0-edit',
    EXCLUIR: '#element-0-remove',
    REMOVER: '#action-remover',
    SELECT_ANGULAR: 'ds3-select',
    SELECT_OPTIONS: '.ds3-select-options'
};

// ==================== MÓDULO MENU LATERAL ====================

export const MenuLateralPage = {
    ICONE_CONFIGURACAO: '#taskbar-bottom-nav-config > .pct',
    TELA_INTEGRACAO: '#sidebar-item-config-integracoes > .pacto-item-menu > .pacto-item-menu-content',
    TELA_TREINO: '#sidebar-item-config-treino > .pacto-item-menu > .pacto-item-menu-content',
    BOTAO_AVANCAR: '#btn-avancar > .content',
    ADM: '#ADM',
    CRM: '#CRM',
    FIN: '#FINANCEIRO',
    TREINO: '#TREINO',
    PAY: '#PACTO_PAY',
    LOG_TREINO: '#show-log',
    FECHAR_LOG: '.close > span'
};

// ==================== MÓDULO MENU SUPERIOR ====================

export const QRCodePage = {
    MENU_QRCODE: '.pct-qr-code',
    ASSINATURA_DIGITAL: '.qr-code-options > :nth-child(1) > span',
    VACINA: '.qr-code-options > :nth-child(2) > span',
    PARQ: '.qr-code-options > :nth-child(3) > span',
    TEXTO: '.text-acesso > span',
    QRCODE: '.img-qrcode > img',
    BOTAO_FECHAR: '#close-aba'
};

// ==================== MÓDULO NOVO CAIXA ABERTO ====================

export const NovoCaixaAbertoPage = {
    INPUT_BUSCA_RAPIDA: '#input-busca-rapida',
    TABELA_CLIENTE: '.table-content',
    CHECK_TODAS_PARCELAS_ALUNO: '#ds3-checkbox__custom-table-row-0-cell-cliente-checkbox-group',
    SELECT_CONVENIO: 'ds3-select[valuekey="codigo"]',
    DS3_SELECT_OPTIONS: '.ds3-select-options',
    BOTAO_RECEBER: '#caixa-em-aberto-btn-receber',
    BOTAO_RECEBER_CAIXA_ABERTO: '#caixa-em-aberto-btn-receber',
    SELECT_FORMA_PAGAMENTO: 'ds3-select[placeholder="Selecione a forma de pagamento"]',
    MODAL_FINALIZAR: '#finalizar',
    SELECIONAR_FORMA_PAGAMENTO: 'span.ds3-select-value-placeholder',
    SELECIONAR_ADQUIRENTE: '#carta-credito-offline-0-select-adquirente > .ds3-select',
    INPUT_VALOR_RECEBIDO: '#input-valor-recebido',
    CONFIRMAR_RECEBIMENTO: '#btn-confirmar-recebimento'
};

// ==================== MÓDULO VENDAS ONLINE ====================

export const CadastroCartaoPage = {
    NOME_TITULAR: '#idnomecartao',
    NUMERO_CARTAO: '#idnrcartao',
    VALIDADE: '#idvalidade',
    CODIGO_SEGURANCA: '#idcvv',
    CHECK_LGPD: ':nth-child(5) > .col-12 > pacto-input > .form-group > .form-control',
    ADICIONAR_CARTAO: '.pacto-btn-primary'
};

// ==================== MÓDULO JSF ====================

// JSF - Páginas Principais
export const AfastamentoPage = {
    PLANO_ALUNO: ':nth-child(4) > .column-cell > .ng-star-inserted',
    AFASTAMENTO: '#dcvg-afastamento-contrato',
    FERIAS: '#form\\:linkAfasCarencia',
    ALTERAR_DIAS_PERMITIDOS: '.fa-icon-edit',
    INPUT_PIN: '#formSenhaAutorizacao\\:senha',
    CONFIRMAR_SENHA: '#formSenhaAutorizacao\\:btnAutorizar',
    INPUT_DIAS_PERMITIDOS: '#form\\:innumCarenciaPer',
    DATA_INICIAL_FERIAS: '#form\\:dataInicioInputDate',
    DATA_FINAL_FERIAS: '#form\\:dataTerminoInputDate',
    CONFIRMAR_DIAS_PERMITIDOS: '#form\\:btnConfirmarAlteracao > .fa-icon-ok',
    JUSTIFICATIVA: '#form\\:justificativa',
    NOME_ALUNO: '#form\\:nomeAlunoCar',
    CONFIRMAR_FERIAS: '#form\\:confirmar',
    BOTAO_IMPRIMIR_COMPROVANTE: '#form\\:comprovanteOpCa',
    MENSAGEM_ERRO: '#form\\:msgCarenciaErro',
    BOTAO_CANCELAMENTO: '#form\\:linkCancelamento > .circulo > tbody > tr > td',
    INPUT_DATA_CANCELAMENTO: '#form\\:dataCancelamentoInputDate',
    ICONE_CALENDARIO: '#form\\:dataCancelamentoPopupButton',
    SELECT_JUSTIFICATIVA: '#form\\:tipoOperacao',
    AVANCAR_CANCELAMENTO: '#form\\:proximo',
    DEVOLUCAO: '#form\\:tipoDevolucaoCancelamento\\:1',
    AVANCAR_DETALHES_CANCELAMENTO: '#formCancelamentoDevolucaoAuto\\:proximo',
    CHECK_QUITACAO_CANCELAMENTO: '#form\\:checkQuitacaoCancelamento',
    FINALIZAR: '#form\\:confirmar'
};

export const BICRMPage = {
    TITULO_BI: '.bi-titulo',
    FILTROS: '.filtros-container',
    ATUALIZAR_DADOS: '#btn-atualizar-bi',
    EXPORTAR_DADOS: '#btn-exportar-dados'
};

export const CaixaEmAbertoPage = {
    DETALHE_PARCELAS: '.fa-icon-plus-sign',
    SELECIONA_TODAS_PARCELAS: '#form\\:tabelaItens\\:0\\:selecionarTodasParcelas',
    TABELA_PARCELAS: '#form\\:tabelaItens\\:0\\:tabelaParcelas\\:0\\:tabelaParcelaValor',
    BOTAO_GERAR_BOLETO: '#form\\:btnGerarBoletoCaixa',
    SELECIONAR_BOLETO: '#form\\:comboConvenioBoletoPadrao',
    BOTAO_GRAVAR_BOLETO: '#form\\:btnGravar',
    CLIENTE: '#form\\:valorConsulta',
    BOTAO_BUSCAR_PARCELAS: '#form\\:botaoBuscarParcelas',
    BOTAO_RECEBER: '#form\\:btnReceberCaixaAberto',
    PRIMEIRA_PARCELA: '#form\\:tabelaItens\\:0\\:tabelaParcelas\\:0\\:selecionarParcela',
    LOADER: '.rich-mpnl-body > img',
    RENEGOCIAR_PARCELAS: '#form\\:tabelaItens\\:0\\:renegociarParcelas',
    INPUT_QTD_PARCELAS: '.classDireita > input'
};

export const CobrancaAutoBloqueadaPage = {
    FILTRO_DATA: '#filtro-data-cobranca',
    BUSCAR_COBRANCAS: '#btn-buscar-cobrancas',
    TABELA_COBRANCAS: '.tabela-cobrancas',
    DESBLOQUEAR_COBRANCA: '#btn-desbloquear'
};

export const ColaboradorPage = {
    CADASTRAR_NOVO: '#form\\:btnNovo',
    NOME_COLABORADOR: '#form\\:nome',
    DATA_NASCIMENTO: '#form\\:dataNascInputDate',
    SALVAR: '#form\\:salvar',
    DADOS_COLABORADOR: '#form\\:abaDadosColaborador_lbl',
    TIPO_COLABORADOR: '#form\\:tipoColaborador',
    ADICIONAR: '#form\\:addTipoColaborador'
};

export const ContatoPessoalPage = {
    BOTAO_CONSULTAR: '#formRC\\:consultar',
    TIPO_CONTATO: '#form\\:tipoOperacao',
    COMENTARIO: '#form\\:observacaoHistorico',
    BOTAO_SIMPLES_REGISTRO: '#form\\:btnSimplesRegistro',
    HISTORICO_LIGACOES: '#modalHistoricoContadorClienteContentTable',
    CONSULTAR: '#formRC\\:valorConsulta',
    OBJECAO: '#form\\:btnObjecao',
    SELECIONAR_OBJECAO: '#form\\:objecao',
    BOTAO_CONCLUIR_OBJECAO: '#form\\:concluirObjecao',
    LINK_CARTAO: '#form\\:linkCadastroCartaoOnline',
    ADD_TAG_EMAIL: '#form\\:tagEmail',
    ENVIAR_MAIL: '#form\\:gravarEnviarEmail',
    TAG_EMPRESA: '#formMarcadorEmail\\:MarcadoEmail\\:0\\:j_id_jsp_560404540_41pc4 > .fa-icon-plus-sign'
};

export const ConvenioCobrancaPage = {
    ADICIONAR_CONVENIO: '#btn-add-convenio',
    NOME_CONVENIO: '#nome-convenio-input',
    TIPO_CONVENIO: '#tipo-convenio-select',
    SALVAR_CONVENIO: '#btn-save-convenio'
};

export const EmpresaPage = {
    DADOS_EMPRESA: '#form\\:dadosEmpresa',
    NOME_EMPRESA: '#form\\:nomeEmpresa',
    CNPJ: '#form\\:cnpj',
    ENDERECO: '#form\\:endereco',
    SALVAR_EMPRESA: '#form\\:salvarEmpresa'
};

// JSF - Módulo Financeiro
export const CaixaAdministrativoPage = {
    TITULO_PAGINA: '.margin-box > .container-header-titulo',
    DATA_ABERTURA: '#form\\:dataAbertura',
    BOTAO_FECHAR_CAIXA: '#form\\:fecharCaixaBtn',
    MENSAGEM: '#form\\:msgCaixa'
};

export const TelaFormasDePagamentoPage = {
    SELECIONAR_QUANTIDADE: '#tblFormaPagamento_lengthId',
    BOTAO_CADASTRAR: '#form\\:btnNovo',
    DESCRICAO: '#form\\:descricao',
    TIPO_PAGAMENTO: '#form\\:tipoFormaPagamento',
    SOMENTO_FINANCEIRO: '#form\\:somenteFinanceiro',
    SELECIONAR_PINPAD: '#form\\:pinpad',
    DESCRICAO_PINPAD_CAPPTA: '#form\\:descricaoPinpadCappta',
    SERIAL_CAPPTA: '#form\\:serialNumberCappta',
    USAR_PINPAD: ':nth-child(5) > .classDireita > input',
    ADD_PINPAD: '#form\\:adicionarPinpad',
    REMOVER_PINPAD: '#form\\:j_id_jsp_1090633582_141\\:0\\:removerPinPad',
    BOTAO_SALVAR: '#form\\:salvar',
    BOTAO_CONFIRMAR_SIM: '#formMdlMensagemGenerica\\:sim',
    BOTAO_EXCLUIR: '#form\\:excluir'
};

export const ContaPagarPage = {
    PAGAR_PARA: '#formLanc\\:pessoa',
    ALUNO_SELECIONADO: '.richfaces_suggestionEntry > :nth-child(2)',
    GRAVAR: '#formLanc\\:btnGravarLanc',
    DESCRICAO: '#formLanc\\:descricao',
    INPUT_DATA_VENCIMENTO: '#formLanc\\:dataVencimentoInputDate',
    DATA_VENCIMENTO_CALENDARIO: '#formLanc\\:dataVencimentoPopupButton',
    DATA_COMPETENCIA_CALENDARIO: '#formLanc\\:dataCompetenciaPopupButton',
    DATA_HOJE: '#formLanc\\:dataVencimentoFooter > table > tbody > tr > :nth-child(5) > .rich-calendar-tool-btn',
    DATA_HOJE_COMPETENCIA: '#formLanc\\:dataCompetenciaFooter > table > tbody > tr > :nth-child(5) > .rich-calendar-tool-btn',
    LOADER: '.rich-mpnl-body > img',
    INPUT_SENHA: '#formSenhaAutorizacao\\:senha',
    CONFIRMAR: '#formSenhaAutorizacao\\:btnAutorizar'
};

export const ContaAReceberPage = {
    CONSULTAR_PLANOS: '#formLanc\\:btAddPlano',
    EXPANDIR_RECEITA: '[id="treeview:1::tNodeP:handles"]',
    RELATORIO_PLANOS: '#treeview'
};

// JSF - Módulo CRM
export const MetaDiariaPage = {
    RECEPTIVO: '#form\\:listaMetaCRM\\:4\\:dtglistaMetaCRM\\:1\\:tipoMetaStyleClass',
    NOME_PASSIVO: '#form\\:nomePassivo',
    CELULAR: '#form\\:telCelularPassivo',
    AGENDAR: '#form\\:apresentarAgendarPassivo',
    AULA_EXPERIMENTAL: '#form\\:agendamentoPassivo\\:0',
    SELECT_MODALIDADE: '#form\\:modalidadeAgendaPassivo',
    TIPO_PROFESSOR: '#form\\:selTipoColabRecep',
    INPUT_HORA: '#form\\:horaMinutoAgendamentoPassivo',
    SELECT_PROFESSOR: '#form\\:selColabRecep',
    CONCLUIR_PASSIVO: '#form\\:concluirAgendaPassivo',
    BUSCAR_AULAS: '#form\\:buscarAulasRecep',
    SELECT_AULAS: '#form\\:selAulaRecep',
    FECHAR_MODAL: '#formMdlMensagemGenerica\\:btnFecharModalGenerico'
};

export const ConfiguracoesCRMPage = {
    DIRECIONAR_AGENDAMENTO: '#form\\:direcionaragendamentosexperimentaisagenda',
    GRAVAR: '#form\\:salvar',
    ABA_DADOS_PESSOAIS: '#form\\:dadosBasico_lbl',
    ABA_EMAIL: '#form\\:configuracaoEmail_lbl',
    REMETENTE_PADRAO: '#form\\:textColaboradorResponsavel',
    SELECIONAR_REMETENTE: '.richfaces_suggestionEntry > .rich-sb-cell-padding',
    REMOVER_REMENTENTE: '#form\\:removeRemetnete'
};

// JSF - Módulo Administrativo
export const NegociacaoJSFPage = {
    SELECT_PLANO: '#form\\:plano',
    LOADER: '.rich-mpnl-body > img',
    DURACAO_MENSAL: '#form\\:planoDuracaoVO\\:0\\:selectDuracaoPlano',
    EM_12_VEZES: '#form\\:planoCondicaoPagamentoVO\\:12\\:btnMarcarCondicaoPagamento > .fa-icon-circle-blank',
    CONFERIR_NEGOCIACAO: '#form\\:btnConferirNegociacao',
    ADICIONAR_PRODUTO: '#form\\:btnAdicionarProdutoPlano',
    CONSULTAR: '#formProduto\\:formProduto',
    TABELA_PRODUTOS: '[id="formProduto:resultadoConsultaProduto:tb"]',
    DESCONTO_PRIMEIRO_PRODUTO: '#form\\:planoProdutoVO\\:1\\:descontoPlano > .fa-icon-plus-sign',
    DESCONTO_SEGUNDO_PRODUTO: '#form\\:planoProdutoVO\\:2\\:descontoPlano > .fa-icon-plus-sign',
    INPUT_DESCONTO_MANUAL: '#formDescontoPlanoProdutoSugerido\\:inputValorDescontoManual',
    INPUT_PIN: '#formSenhaAutorizacao\\:senha',
    CONFIRMAR_PIN: '#formSenhaAutorizacao\\:btnAutorizar',
    FECHAR_MODAL_DESCONTO: '#hidelink4',
    VALOR_TOTAL_PRODUTOS: '#form\\:valorTotalProdutos'
};

export const FechamentoNegociacaoJSFPage = {
    PRO_RATA: '#form\\:diasVencimento',
    VALOR_PRIMEIRA_PARCELA: '#form\\:movProduto\\:0\\:totalMensalidade',
    CONCLUIR: '#form\\:botaoConfirmarcao',
    INPUT_SENHA: '#formSenhaAutorizacao\\:senha',
    CONFIRMAR_SENHA: '#formSenhaAutorizacao\\:btnAutorizar'
};

export const ProdutoColetivoJSFPage = {
    NOVO: '#form\\:btnNovo',
    DESCRICAO: '#form\\:descricao',
    PRODUTO: '#form\\:produto',
    SUGESTAO: '.richfaces_suggestionEntry .rich-sb-cell-padding',
    PLANO: '#form\\:plano',
    SUGESTAO_PLANO: '#form\\:suggestionPlano\\:suggest > tbody > .richfaces_suggestionEntry > .rich-sb-cell-padding',
    LANCAR_PRODUTO: ':nth-child(11) > .classDireita > .tooltipster',
    SALVAR: '#form\\:salvar',
    SENHA: '#formSenhaAutorizacao\\:senha',
    CONTRATO_SEM_PRODUTO: '#form\\:j_id_jsp_1290575515_28\\:3',
    CONFIRMAR: '#formSenhaAutorizacao\\:btnAutorizar'
};

export const OrcamentoPage = {
    MENU_CRIAR_ORCAMENTO: '#sidebar-item-orcamento > .pacto-item-menu > .pacto-item-menu-content',
    SELECT_CONSULTOR: '#form\\:consultor',
    PROSPECTO: '#form\\:nomeCliente',
    PROSPECTO_SELECIONADO: '.rich-sb-cell-padding > .texto-font',
    TIPO_PROSPECTO: '#form\\:paraquem',
    INPUT_TIPO_PROSPECTO: '#form\\:nomeProspecto',
    INPUT_IDADE_PROSPECTO: '#form\\:idade',
    SELECT_ORCAMENTO: '#form\\:modeloorcamento',
    PERIODO: '#form\\:periodo',
    SITUACAO: '#form\\:situacao',
    TIPO_TURMA: '#form\\:tipoturma',
    CONCLUIR: '#form\\:salvar'
};

export const TelaDoClienteJSFPage = {
    BOTAO_NOVO_CONTRATO: '#form\\:btnNovoContratoLista'
};

export const VendasOnlineJSFPage = {
    PAINEL: '#form\\:vendasOnlinePanel',
    LOG: '#form\\:visualizarLogVendasOnlineConfig',
    BOTAO_VISUALIZAR_LOG: '#form\\:visualizarLogVendasOnlineConfig\\.botaoSecundario\\.texto-size-14-real\\.fa-icon-list',
    ABA_GESTAO: '#form\\:abaGestao',
    ICON_COGS: '.fa-icon-cogs',
    INTEGRACOES: '#form\\:integracoesVendasOnline',
    CHAVE_ANALYTICS: '#form\\:chaveAnalytics',
    PIXEL_FACEBOOK: '#form\\:pixelFacebook',
    API_CONVERSAO_FB: '#form\\:apiConversaoFacebook',
    GTM: '#form\\:googleTagManager',
    CHECK_BOT_CONVERSA: '#form\\:habIntBotConversa',
    ENDPOINT_API: '#form\\:endpointAPI',
    BTN_GRAVAR_CONFIG: '#form\\:gravarVendasConfig',
    MENSAGEM_SUCESSO: '#messageInfo'
};

// ==================== MÓDULO ELEMENTOS JS CONVERTIDOS ====================

export const AddVeiculoPage = {
    MODULONTR: '[data-cy=',
    CAMPOBUSCA: '#topbar-search-field',
    BTNDADOSPESSOAIS: '#pch-link-dados-pessoais',
    TABACESSOCATRACA: '#cli-tab-acesso-catraca',
    INPUTPLACA: '#input-text-table-familiar-placa',
    INPUTDESCRICAO: '#input-text-table-familiar-descricaoVeiculo',
    BTNCONFIRMAR: '#element-0-confirm-table-familiar',
    BTNLOG: '#pch-btn-log > .content',
    LOGATIVIDADE: 'pacto-log-atividade.ng-star-inserted > pacto-cat-layout-v2 > .pct-content',
    ADDCLIENTE: '#form\\:linkaddClienteMenuSuperiorZWUI > .pct',
    CONSULTA: '#form\\:termosConsulta',
    CONSULTACLIENTE: '#form\\:btnConsultarCliente',
    NOVO: '#form\\:btnNovo',
    SALVAR: '[id=',
    CONSULTOR: '#form\\:consultor',
    VISITANTE: '#form\\:cadastrarVisitante2',
    SALVARSIM: '#formGravarBV\\:btnSimCadastrarVisitante',
    LUPA: '#topbar-search-field'
};

export const BiAdmPendenciasPage = {
    EXPLORAR: '[id=',
    BI: '.menu-ADM_BUSINESS_INTELLIGENCE',
    CONTAINERRECORRENCIA: '[id=',
    PENDENCIAPARCELASEMABERTO: '[id='
};

export const BiInadimplenciaPage = {
    PREVISAO: '#form\\:previsao',
    PARCELASABERTAS: '#v20_form > :nth-child(9) > :nth-child(1) > :nth-child(1) > :nth-child(2)',
    RECEBIDOS: '#form\\:recebidos',
    INADIMPLENCIA: '#form\\:inadimplencia',
    ATUALIZARINADIMPLENCIA: '#form\\:atualizarInadimplencia',
    CARDABERTO: 'table[style*='
};

export const CancelamentoPadraoComTransferenciaPage = {

};

export const DescontoValorPage = {

};

export const GestaoDeArmariosPage = {
    SITUACOES: '#form\\:tpSituacoes',
    GRUPOS: '#form\\:tpGrupos',
    TAMANHOS: '#form\\:tpTamanhos',
    BUSCAARMARIO: '#form\\:campoBuscarArmario',
    NOVOARMARIO: '#form\\:novoArmario',
    MODALINPUTIDDE: '#formModalGerarArmarios\\:id_de',
    MODALINPUTIDATE: '#formModalGerarArmarios\\:id_ate',
    BOTAOINSERIRARMARIOS: '#formModalGerarArmarios\\:inserirArmarios',
    MENSAGEMSUCESSOCADASTRO: 'Armários gerados com sucesso'
};

export const TrancamentoParcelasPage = {

};

export const AparelhoCrossPage = {

};

export const AtividadesPage = {

};

export const CadastroNiveisPage = {

};

export const CarrocelDeModulosPage = {
    TREINOMODULE: '[data-cy=',
    CRMMODULE: '[data-cy=',
    FINANCEIROMODULE: ':nth-child(1) > :nth-child(4) > [data-cy=',
    AGENDAMODULE: '[data-cy=',
    PACTOPAYMODULE: '[data-cy=',
    AVALIACAOMODULE: '[data-cy=',
    CROSSMODULE: '[data-cy=',
    GRADUACAOMODULE: '[data-cy=',
    ADMINMODULE: '#module-adm_legado',
    SIDEBARCURRENTMODULE: '#sidebar-current-module',
    SIDEBARJSFCURRENTMODULE: '#zwUiModuloAtualDescricao',
    MODULENAME: '[class=module-name]',
    CAROUSELNAVDOWN: '#modules-menu-nav-down',
    MODULEPAGE2: '#modules-menu-page-2',
    TASKBARARROWUP: '.taskbar-arrow-up > .pct',
    MODULOADM: '[data-cy=',
    LEGADO: '#module-adm_legado'
};

export const FichasPredefinidasPage = {

};

export const MenuCrossPage = {

};

export const Meta24HorasPage = {
    MODULOSIGLACRM: '[data-cy=',
    METADIARIA: '.CRM_META_DIARIA',
    LISTAMETACRMOBJETIVO: '#form\\:listaMetaCRM\\:1\\:dtglistaMetaCRM\\:3\\:fasesCRMEnumObjetivo',
    LISTACLIENTES: '#form\\:listaClientes',
    FOTOSNANUVEMTRUEZWUI: 'img[id='
};

export const NivelWodPage = {

};

export const TiposAgendamentoPage = {

};

export const TiposWODCrossPage = {

};
// ==================== LOCATORS CORRIGIDOS ====================

export const TrancamentoParcelasPage = {
    VALOR_CONSULTA: '#form\:valorConsulta',
    CONSULTAR: '#form\:consultar',
    NOME_CLIENTE: '#form\:tabelaCliente\:0\:nomeCliente',
    SELECIONAR_CONTRATO: '#form\:listaContrato\:0\:linkMatriculaSelecionarContrato',
    LINK_AFASTAMENTO: '#form\:linkAfastamento',
    LINK_TRANCAMENTO: '#form\:linkAfasTrancamento',
    DATA_TRANCAMENTO: '#form\:dataTrancamentoInputDate',
    PROXIMO: '#form\:proximo',
    PRODUTO_TRANCAMENTO: '#form\:produtoTrancamento',
    JUSTIFICATIVA: '#form\:justificativa',
    ALTERAR_PARCELAS: '#form\:alterarParcelas',
    CONFIRMAR: '#form\:confirmar',
    SENHA_AUTORIZACAO: '#formSenhaAutorizacao\:senha',
    BTN_AUTORIZAR: '#formSenhaAutorizacao\:btnAutorizar',
    COMPROVANTE: '#form\:comprovanteOpTr',
    // Propriedades adicionais encontradas no código (camelCase)
    selecionarContrato: '#form\:listaContrato\:0\:linkMatriculaSelecionarContrato',
    linkAfastamento: '#form\:linkAfastamento',
    linkTrancamento: '#form\:linkAfasTrancamento',
    dataTrancamentoInputDate: '#form\:dataTrancamentoInputDate',
    proximo: '#form\:proximo',
    produtoTrancamento: '#form\:produtoTrancamento',
    justificativa: '#form\:justificativa',
    alterarParcelas: '#form\:alterarParcelas',
    confirmar: '#form\:confirmar',
    senhaAutorizacao: '#formSenhaAutorizacao\:senha',
    btnAutorizar: '#formSenhaAutorizacao\:btnAutorizar',
    comprovanteOpTr: '#form\:comprovanteOpTr',
    historicoParcelasDataVencimento: '#form\:historicoParcelasDataVencimento',
    estornarOperacao: '#form\:estornarOperacao',
    linkVerMaisFinanceiro: '#form\:linkVerMaisFinanceiro'
};

export const Meta24HorasPage = {
    MODULO_SIGLA_CRM: '[data-cy="modulo-sigla-CRM"]',
    META_DIARIA: '.CRM_META_DIARIA',
    LISTA_META_CRM_OBJETIVO: '#form\:listaMetaCRM\:1\:dtglistaMetaCRM\:3\:fasesCRMEnumObjetivo',
    LISTA_CLIENTES: '#form\:listaClientes',
    FOTOS_NA_NUVEM_TRUE_ZWUI: 'img[id="form:fotosNaNuvemTrueZwui"]'
};

export const TiposWODCrossPage = {
    ARROW_DOWN_MODULOS: '.taskbar-arrow-down > .pct',
    MODULOS_MENU_NAV: '#modules-menu-nav-down > img',
    MODULO_CROSS: '[data-cy="modulo-sigla-NCR"]',
    BI_CROSS: '[data-cy="CROSS_BI"] > .pacto-item-menu-content',
    CADASTROS: '#sidebar-item-cadastros > .pacto-item-menu > .pacto-item-menu-content',
    TIPOS_DE_WOD: '[data-cy="CROSS_TIPO_WOD"] > .pacto-item-menu-content',
    ADICIONAR_TIPO_DE_WOD: '#btn-novo-tipo-wod',
    NOME: '.form-group > .form-control',
    SALVAR_TIPO_DE_WOD: '.btn-primary',
    EXCLUIR: '#element-4-remove',
    CONFIRMAR: '#action-remover',
    LOG: '#show-log',
    FECHAR_LOG: '.close',
    EDITAR: '#element-0-edit'
};

export const AparelhoCrossPage = {
    BUSCA_RAPIDA: '#input-busca-rapida',
    CADASTROS: '#menu-crossfit-cadastros',
    CADASTROS_ATUALIZADO: '#sidebar-item-cadastros > .pacto-item-menu > .pacto-item-menu-content',
    MODULO_TREINO: '[data-cy="modulo-sigla-NTR"]',
    APARELHOS: '#menu-crossfit-aparelhos > .menu-name',
    APARELHOS_ATUALIZADO: '[data-cy="TREINO_APARELHOS"] > .pacto-item-menu-content',
    ADICIONAR_APARELHO: '#btn-novo-aparelho',
    NOME_APARELHO: '#nome-aparelho-input',
    AJUSTES: '.input-row > .form-group > .form-control',
    ADICIONAR_AJUSTES: '.input-row > .btn',
    SALVAR_APARELHO: '#btn-add-aparelho',
    EXCLUIR_APARELHO: '#element-0-remove',
    CONFIRMAR: '#action-remover',
    LOG_APARELHO: '#show-log',
    FECHAR_LOG: '.close'
};

export const CadastroNiveisPage = {
    MODULO_TREINO: '[data-cy="modulo-sigla-NTR"]',
    EXPANDIR_MENU_LATERAL: '.menu-toggle > .pct ',
    CADASTROS: '#sidebar-item-cadastros > .pacto-item-menu > .pacto-item-menu-content',
    NIVEIS: '[data-cy="TREINO_NIVEIS"] > .pacto-item-menu-content',
    ADICIONAR_NIVEL: '#adicionarNivel',
    NOME_NIVEL: '#input-nome-nivel',
    ORDEM_NIVEL: '#input-ordem-nivel',
    SALVAR_NIVEL: '#gravarCadastroNivel',
    INATIVAR_NIVEL: '#element-0-desativar',
    CONFIRMAR_INATIVADO: '#action-desativar',
    EXCLUIR_NIVEL: '#element-0-remove',
    CONFIRMAR: '#action-remover',
    LOG_APARELHO: '#show-log',
    FECHAR_LOG: '.close'
};

export const FichasPredefinidasPage = {
    MODULO_TREINO: '[data-cy="modulo-sigla-NTR"]',
    CADASTROS: '#sidebar-item-cadastros > .pacto-item-menu > .pacto-item-menu-content',
    FICHAS_PREDEFINIDAS: '[data-cy="TREINO_FICHAS_PREDEFINIDAS"]',
    ADICIONAR: '#btn-nova-ficha-predefinida',
    NOME_DA_FICHA: '#nome-ficha-input',
    CATEGORIA_DE_FICHA: '#categoria-select',
    ADICIONAR_VARIAS_ATIVIDADES: '#adicionar-varias-atividades > .content',
    ATIVIDADE_1: ':nth-child(2) > .block-actions > .btn-add',
    ATIVIDADE_2: ':nth-child(3) > .block-actions > .btn-add',
    ATIVIDADE_3: ':nth-child(4) > .block-actions > .btn-add',
    FECHAR_MODAL_ATIVIDADES: '.col-md-3 > .pct',
    ALTERACOES_SALVAS: '.salvando-status',
    VOLTAR_TELA: '.back > .pct',
    EDITAR: '#element-3-edit'
};

export const MenuCrossPage = {
    BI_CROSS: '[data-cy="CROSS_BI"] > .pacto-item-menu-content',
    ALUNOS_ATIVOS: ':nth-child(1) > pacto-small-info-card.ng-star-inserted > .small-info-card-wrapper',
    INPUT_BUSCA_RAPIDA: '#input-busca-rapida',
    FECHA_MODAL: '.close > span',
    MONITOR_MENU: '[data-cy="CROSS_MONITOR"]',
    WOD_MENU: '[data-cy="CROSS_WOD"]',
    CADASTROS: '#sidebar-item-cadastros',
    GESTAO_DE_PERSONAL: '#menu-treino-personal > .menu-name',
    EXPLORAR: '#topbar-modules-toggle',
    EXPLORAR_BI: '#explorar-menu-ncr-bi-cross',
    EXPLORAR_OP: '#global-menu-funcionalidade-operacoes > .nome',
    MENU_CADASTRO: '#sidebar-item-cadastros > .pacto-item-menu > .pacto-item-menu-content',
    EXPLORAR_PERSONAL: '#global-menu-funcionalidade-personal > .nome'
};

export const AtividadesPage = {
    MODULO_DOWN: '#modules-menu-nav-down > img',
    MODULO_CROSS: '[data-cy="modulo-sigla-NCR"]',
    CADASTROS: '#sidebar-item-cadastros > .pacto-item-menu > .pacto-item-menu-content',
    ATIVIDADES: '[data-cy="CROSS_ATIVIDADE"]',
    ADICIONAR: '#btn-novo-atividade-crossfit',
    NOME_ATIVIDADE: '#nome-atividade-crossfit-input',
    SALVAR: '#btn-add-atividade-crossfit',
    EDITAR: '#element-0-edit',
    CATEGORIA_ATIVIDADE: '#categoria-atividade-select',
    UNIDADE_MEDIDA: '#unidade-medida-select',
    REMOVER: 'i[id^="element-"].fa.fa-minus-square-o.ng-star-inserted',
    INATIVAR: '#action-inativar'
};

export const TiposAgendamentoPage = {
    ADICIONAR: '#btn-novo-tipo-agendamento',
    NOME: '#nome-tipo-agendamento-input',
    NUMERO: '#nr-tipo-agendamento-input',
    COMPORTAMENTOS: '#select-comportamento',
    SELECIONAR_COR: '#btn-selecione-cor',
    AZUL: '#cor-0',
    SALVAR: '#btn-add-tipo-agendamento',
    NOTIFICACAO: '.snotifyToast__inner',
    NA_CARTEIRA: '#defaultCheck2',
    ATIVO: '#status-tipo-agendamento',
    APP: '#permitir-app',
    EDITAR: '#element-0-edit',
    DURACAO: '#select-tipo-duracao',
    TEMPO_EM_HORAS: '#input-duracao-pre-definida',
    MINIMO: '[label="Mínimo em horas e minutos"] > .form-group > .form-control',
    MAXIMO: '[label="Máximo em horas e minutos"] > .form-group > .form-control',
    EXCLUIR: '#element-0-remove',
    CONFIRMAR_EXCLUSAO: '#action-remover',
    BOTAO_LOG: '#show-log > .pct'
};