/**
 * Script para completar a refatoração das pages JSF/ADM que ainda têm objetos elements locais
 */

const fs = require('fs');
const path = require('path');

// Mapeamento de nomes de classes para objetos de locators JSF/ADM
const pageMapping = {
    'ConfiguracaoClubeVantagens': 'ConfiguracaoClubeVantagensPage',
    'ConfiguracaoClubeVantagensClass': 'ConfiguracaoClubeVantagensPage',
    'NivelTurmaPage': 'NivelTurmaJSFPage',
    'NivelTurmaJSFPage': 'NivelTurmaJSFPage',
    'NivelTurmaJSFPageClass': 'NivelTurmaJSFPage',
    'NegociacaoJSFPage': 'NegociacaoJSFPage',
    'NegociacaoJSFPageClass': 'NegociacaoJSFPage',
    'ProdutoColetivoJSFPage': 'ProdutoColetivoJSFPage',
    'ProdutoColetivoJSFPageClass': 'ProdutoColetivoJSFPage',
    'FechamentoNegociacaoJSFPage': 'FechamentoNegociacaoJSFPage',
    'FechamentoNegociacaoJSFPageClass': 'FechamentoNegociacaoJSFPage'
};

function cleanupElementsObject(filePath, className, locatorObject) {
    console.log(`Limpando objetos elements de ${filePath}...`);
    
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Verificar se já tem o import correto
    const importStatement = `import { ${locatorObject} } from '../../../locators';`;
    
    if (!content.includes(`import { ${locatorObject} }`)) {
        // Adicionar import se não existir
        const lines = content.split('\n');
        let insertIndex = 0;
        
        // Procurar por imports existentes
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].trim().startsWith('import ')) {
                insertIndex = i + 1;
            } else if (lines[i].trim() === '' && insertIndex > 0) {
                continue;
            } else if (lines[i].trim() !== '' && !lines[i].trim().startsWith('import')) {
                break;
            }
        }
        
        lines.splice(insertIndex, 0, importStatement);
        content = lines.join('\n');
    }
    
    // Remover imports incorretos
    content = content.replace(/import\s*\{\s*[^}]*\}\s*from\s*['"]\.\.\/locators['"];\s*/g, '');
    content = content.replace(/import\s*\{\s*[^}]*\}\s*from\s*['"]\.\.\/\.\.\/locators['"];\s*/g, '');
    
    // Remover objetos elements (incluindo static e private)
    content = content.replace(/\s*(private\s+)?(static\s+)?(readonly\s+)?elements\s*=\s*\{[\s\S]*?\};\s*/g, '\n\n');
    content = content.replace(/\s*elements\s*=\s*\{\s*\}\s*/g, '\n\n');
    
    // Substituir this.elements.ELEMENTO por LocatorObject.ELEMENTO
    content = content.replace(/this\.elements\.([A-Z_][A-Z0-9_]*)/g, `${locatorObject}.$1`);
    
    // Substituir ClassName.elements.ELEMENTO por LocatorObject.ELEMENTO (para static elements)
    content = content.replace(new RegExp(`${className}\\.elements\\.([A-Z_][A-Z0-9_]*)`, 'g'), `${locatorObject}.$1`);
    
    // Renomear a classe para evitar conflitos (se necessário)
    if (!content.includes(`class ${className}Class`)) {
        content = content.replace(new RegExp(`class ${className}`, 'g'), `class ${className}Class`);
        content = content.replace(new RegExp(`export default new ${className}\\(\\)`, 'g'), `export default new ${className}Class()`);
    }
    
    // Remover linhas vazias excessivas
    content = content.replace(/\n\n\n+/g, '\n\n');
    
    fs.writeFileSync(filePath, content);
    console.log(`✅ ${filePath} limpo com sucesso!`);
}

function scanDirectory(dir, basePath = '') {
    const files = [];
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
            files.push(...scanDirectory(fullPath, path.join(basePath, item)));
        } else if ((item.endsWith('.ts') || item.endsWith('.js'))) {
            files.push({
                fullPath,
                relativePath: path.join(basePath, item),
                fileName: item,
                className: item.replace(/\.(ts|js)$/, '')
            });
        }
    });
    
    return files;
}

function main() {
    const jsfAdmDir = path.join(__dirname, 'cypress/support/pages/jsf/adm');
    
    console.log('🧹 Completando limpeza das pages JSF/ADM...\n');
    
    // Escanear todos os arquivos
    const allFiles = scanDirectory(jsfAdmDir);
    
    console.log(`📁 Encontrados ${allFiles.length} arquivos para verificar\n`);
    
    let cleanedCount = 0;
    let skippedCount = 0;
    
    allFiles.forEach(file => {
        const { fullPath, className, relativePath } = file;
        
        try {
            const content = fs.readFileSync(fullPath, 'utf8');
            
            // Verificar se o arquivo contém objetos elements
            if (content.includes('elements = {')) {
                const locatorObject = pageMapping[className];
                
                if (locatorObject) {
                    cleanupElementsObject(fullPath, className, locatorObject);
                    cleanedCount++;
                } else {
                    console.log(`⚠️  ${relativePath} tem elements mas não está no mapeamento`);
                    skippedCount++;
                }
            } else {
                console.log(`⏭️  Pulando ${relativePath} - não contém objetos elements`);
                skippedCount++;
            }
        } catch (error) {
            console.error(`❌ Erro ao processar ${relativePath}:`, error.message);
        }
    });
    
    console.log('\n✨ Limpeza JSF/ADM concluída!');
    console.log(`📊 Arquivos limpos: ${cleanedCount}`);
    console.log(`📊 Arquivos pulados: ${skippedCount}`);
}

if (require.main === module) {
    main();
}

module.exports = { cleanupElementsObject, pageMapping };
